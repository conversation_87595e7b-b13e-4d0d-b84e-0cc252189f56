# 腾讯翻译API配置说明

## 🔑 获取API密钥

### 1. 注册腾讯云账号
- 访问：https://cloud.tencent.com/
- 注册并完成实名认证

### 2. 开通机器翻译服务
- 进入控制台：https://console.cloud.tencent.com/tmt
- 开通机器翻译服务
- 查看免费额度：每月500万字符

### 3. 创建API密钥
- 访问：https://console.cloud.tencent.com/cam/capi
- 点击"新建密钥"
- 记录 `SecretId` 和 `SecretKey`

## ⚙️ 配置方法

### 方法1：环境变量（推荐）
```bash
# Windows
set TENCENT_SECRET_ID=你的SecretId
set TENCENT_SECRET_KEY=你的SecretKey

# Linux/Mac
export TENCENT_SECRET_ID=你的SecretId
export TENCENT_SECRET_KEY=你的SecretKey
```

### 方法2：配置文件
在项目根目录创建 `.env` 文件：
```
TENCENT_SECRET_ID=你的SecretId
TENCENT_SECRET_KEY=你的SecretKey
```

### 方法3：代码中配置
修改 `src/translation/translator.py`：
```python
self.tencent_translator = TencentTranslator(
    secret_id="你的SecretId",
    secret_key="你的SecretKey"
)
```

## 📊 API限制说明

### 免费额度
- **每月免费**：500万字符
- **超出收费**：0.058元/万字符

### 频率限制
- **请求频率**：5次/秒
- **单次请求**：最大6000字符
- **并发限制**：同时最多5个请求

### 优化策略
我们的程序已经实现了以下优化：

1. **智能批量处理**
   - 自动合并多个短文本到单次请求
   - 最大化利用6000字符限制
   - 减少API调用次数

2. **频率控制**
   - 自动控制请求间隔（1.2秒）
   - 避免触发频率限制
   - 失败自动重试机制

3. **本地优先策略**
   - 优先使用Helsinki-NLP本地翻译
   - 仅在本地翻译失败时使用云端API
   - 大幅减少API使用量

## 🔍 使用量监控

### 查看使用情况
- 控制台：https://console.cloud.tencent.com/tmt
- 查看当月使用量和剩余额度

### 成本估算
以22个技术文档为例：
- 平均每个文档：2000字符
- 总字符数：44,000字符
- 月处理量：<500万字符（在免费范围内）

## ⚠️ 注意事项

1. **密钥安全**
   - 不要将密钥提交到代码仓库
   - 使用环境变量或配置文件
   - 定期更换密钥

2. **额度管理**
   - 监控月使用量
   - 超出免费额度会产生费用
   - 可设置费用告警

3. **网络要求**
   - 需要稳定的网络连接
   - 支持HTTPS请求
   - 可能需要配置代理

## 🚀 测试配置

运行测试脚本验证配置：
```bash
python test_translation.py
```

如果配置正确，会显示：
```
✅ 腾讯翻译API配置成功
✅ 翻译测试通过
```

## 🔧 故障排除

### 常见错误

1. **认证失败**
   ```
   AuthFailure.SecretIdNotFound
   ```
   - 检查SecretId是否正确
   - 确认密钥是否已激活

2. **频率限制**
   ```
   RequestLimitExceeded
   ```
   - 程序会自动处理，无需干预
   - 如果频繁出现，检查并发设置

3. **额度不足**
   ```
   ResourceUnavailable.NotInWhiteList
   ```
   - 检查月使用量是否超限
   - 考虑升级到付费版本

### 联系支持
- 腾讯云工单：https://console.cloud.tencent.com/workorder
- 技术文档：https://cloud.tencent.com/document/product/551

## 📈 性能优化建议

1. **批量处理**
   - 一次处理多个文档
   - 减少启动开销

2. **缓存机制**
   - 相同文本不重复翻译
   - 保存翻译历史

3. **混合策略**
   - 优先本地翻译
   - 云端作为备份

通过以上配置和优化，可以高效、经济地使用腾讯翻译API进行文档翻译。
