#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时测试提取内容工具
用于调试和查看文档内容提取结果
"""

import sys
import os
from pathlib import Path
import json

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.extraction import PDFExtractor, DocxExtractor
from src.summary_table import SummaryTableConfig, TechnicalDataExtractor, ABComponentExtractor
from src.utils import get_logger

logger = get_logger(__name__)


def test_raw_extraction(file_path: str):
    """测试原始内容提取"""
    print("=" * 80)
    print(f"🔍 测试文件: {os.path.basename(file_path)}")
    print("=" * 80)
    
    file_ext = Path(file_path).suffix.lower()
    
    if file_ext == '.pdf':
        extractor = PDFExtractor()
    elif file_ext == '.docx':
        extractor = DocxExtractor()
    else:
        print(f"❌ 不支持的文件格式: {file_ext}")
        return
    
    # 提取内容
    content = extractor.extract_content(file_path)
    
    if not content:
        print("❌ 内容提取失败")
        return
    
    print(f"📄 基本信息:")
    print(f"  标题: {content.title}")
    print(f"  产品名称: {content.product_name}")
    print(f"  表格数量: {len(content.tables)}")
    print(f"  段落数量: {len(content.paragraphs)}")
    print(f"  元数据: {content.metadata}")
    print()

    # 测试产品名称提取的详细过程
    print("🔍 产品名称提取详情:")
    if file_ext == '.docx':
        from docx import Document
        doc = Document(file_path)

        # 测试页眉提取
        header_name = extractor._extract_product_name_from_header(doc)
        print(f"  页眉提取结果: {header_name if header_name else '未找到'}")

        # 显示页眉内容
        for section in doc.sections:
            if section.header:
                for para in section.header.paragraphs:
                    if para.text.strip():
                        print(f"    页眉内容: {para.text.strip()}")

        # 测试文件名提取
        filename_name = extractor._extract_product_name_from_filename(os.path.basename(file_path))
        print(f"  文件名提取结果: {filename_name}")

        # 测试产品陈述提取
        statement_name = extractor._extract_product_name_from_statement(content)
        print(f"  产品陈述提取结果: {statement_name if statement_name else '未找到'}")

    print()
    
    # 显示表格内容
    print("📊 表格内容:")
    for i, table in enumerate(content.tables, 1):
        # 检查是否为嵌套表格
        is_nested = table.position and table.position.get('nested', False) if hasattr(table, 'position') and table.position else False
        nested_info = f" [嵌套表格]" if is_nested else ""

        print(f"  表格 {i} (页面 {table.page}){nested_info}:")

        if is_nested and hasattr(table, 'position') and table.position:
            parent_cell = table.position.get('parent_cell', '')
            if parent_cell:
                print(f"    父单元格: {parent_cell}")

        if table.headers:
            print(f"    表头: {table.headers}")
        print(f"    数据行数: {len(table.data)}")

        # 显示前10行数据
        for j, row in enumerate(table.data[:10]):
            print(f"      行{j+1}: {row}")

        if len(table.data) > 10:
            print(f"      ... (还有 {len(table.data) - 10} 行)")
        print()
    
    # 显示段落内容
    if content.paragraphs:
        print("📝 段落内容:")
        for i, para in enumerate(content.paragraphs[:10], 1):
            print(f"  段落 {i} (页面 {para.page}): {para.text}")
        
        if len(content.paragraphs) > 10:
            print(f"  ... (还有 {len(content.paragraphs) - 10} 个段落)")
        print()


def test_technical_extraction(file_path: str):
    """测试技术参数提取"""
    print("🔧 技术参数提取测试:")
    print("-" * 50)
    
    try:
        # 初始化技术数据提取器
        config = SummaryTableConfig()
        tech_extractor = TechnicalDataExtractor(config)
        
        # 提取技术数据
        result = tech_extractor.extract_from_file(file_path)
        
        if not result:
            print("❌ 技术参数提取失败")
            return
        
        print(f"✅ 技术参数提取成功:")
        print(f"  产品名称: {result.get('product_name', 'N/A')}")
        print(f"  产品型号: {result.get('product_model', 'N/A')}")
        print(f"  文件类型: {result.get('file_type', 'N/A')}")
        print(f"  参数数量: {len(result.get('parameters', {}))}")
        print()
        
        # 显示提取的参数
        parameters = result.get('parameters', {})
        if parameters:
            print("📋 提取的参数:")
            for param_name, param_value in parameters.items():
                print(f"  {param_name}: {param_value}")
        else:
            print("⚠️ 没有提取到参数")
        
        print()
        
        # 显示原始内容摘要
        raw_content = result.get('raw_content', {})
        if raw_content:
            print("📄 原始内容摘要:")
            print(f"  表格数量: {len(raw_content.get('tables', []))}")
            print(f"  段落数量: {len(raw_content.get('paragraphs', []))}")
            
            # 显示原始表格数据
            tables = raw_content.get('tables', [])
            for i, table in enumerate(tables, 1):
                print(f"  原始表格 {i}:")
                table_data = table.get('data', [])
                for j, row in enumerate(table_data[:5]):
                    print(f"    行{j+1}: {row}")
                if len(table_data) > 5:
                    print(f"    ... (还有 {len(table_data) - 5} 行)")
        
    except Exception as e:
        print(f"❌ 技术参数提取出错: {e}")
        import traceback
        traceback.print_exc()


def test_ab_detection(file_path: str):
    """测试A/B组分检测"""
    print("🧪 A/B组分检测测试:")
    print("-" * 50)
    
    try:
        config = SummaryTableConfig()
        tech_extractor = TechnicalDataExtractor(config)
        
        # 先进行原始提取
        file_ext = Path(file_path).suffix.lower()
        if file_ext == '.pdf':
            extractor = PDFExtractor()
        elif file_ext == '.docx':
            extractor = DocxExtractor()
        else:
            print(f"❌ 不支持的文件格式: {file_ext}")
            return
        
        content = extractor.extract_content(file_path)
        if not content:
            print("❌ 内容提取失败")
            return
        
        # 转换为字典格式
        content_dict = tech_extractor._convert_extracted_content_to_dict(content)
        
        # 提取原始参数
        raw_parameters = tech_extractor._extract_parameters_from_content(content_dict)
        
        print(f"📊 原始参数 ({len(raw_parameters)} 个):")
        for param, value in raw_parameters.items():
            print(f"  {param}: {value}")
        print()
        
        # 使用专门的A/B组分提取器
        ab_extractor = ABComponentExtractor()

        # 检测A/B组分
        product_name = content.product_name or os.path.basename(file_path)
        is_ab = ab_extractor.is_ab_product(content_dict, product_name)
        print(f"🔍 A/B组分检测结果: {'是' if is_ab else '否'}")
        print(f"  产品名称: {product_name}")

        # 提取A/B组分参数
        ab_parameters = ab_extractor.extract_ab_components(content_dict)
        if ab_parameters:
            print(f"🧪 A/B组分参数 ({len(ab_parameters)} 个):")
            for param, value in ab_parameters.items():
                print(f"    {param}: {value}")
        else:
            print(f"  未提取到A/B组分参数")

        # 显示A/B表格详细信息
        ab_table = ab_extractor._find_ab_component_table(content_dict)
        if ab_table:
            print(f"📋 A/B组分表格详情:")
            table_data = ab_table.get('data', [])
            ab_columns = ab_extractor._find_ab_columns(table_data)
            if ab_columns:
                a_col, b_col = ab_columns
                print(f"  A列索引: {a_col}, B列索引: {b_col}")
                if len(table_data) > 0 and len(table_data[0]) > max(a_col, b_col):
                    print(f"  A列标题: '{table_data[0][a_col]}'")
                    print(f"  B列标题: '{table_data[0][b_col]}'")

                # 显示前几行数据
                print(f"  表格前5行:")
                for i, row in enumerate(table_data[:5]):
                    if len(row) > max(a_col, b_col):
                        print(f"    行{i+1}: 项目='{row[0]}', A='{row[a_col]}', B='{row[b_col]}'")

                # 显示实际提取的参数对比
                print(f"  实际提取对比:")
                for param, value in ab_parameters.items():
                    if param.endswith('(A)'):
                        param_name = param[:-3]
                        b_param = f"{param_name}(B)"
                        if b_param in ab_parameters:
                            print(f"    {param_name}: A='{value}' vs B='{ab_parameters[b_param]}'")
            else:
                print(f"  未找到A/B列")

        # 显示A/B相关关键词检测
        ab_keywords = ["A组分", "B组分", "A剂", "B剂", "主剂", "固化剂", "混合比例"]
        found_keywords = []
        for param_name in raw_parameters.keys():
            for keyword in ab_keywords:
                if keyword in param_name:
                    found_keywords.append(f"{param_name} (包含: {keyword})")

        if found_keywords:
            print(f"  原始参数中的A/B关键词:")
            for keyword in found_keywords:
                print(f"    - {keyword}")

        print()
        
        # 测试参数映射
        mapped_params = tech_extractor.parameter_mapper.map_parameters(raw_parameters, product_name)
        print(f"🗺️ 映射后参数 ({len(mapped_params)} 个):")
        for param, value in mapped_params.items():
            print(f"  {param}: {value}")
        
    except Exception as e:
        print(f"❌ A/B组分检测出错: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🔧 文档内容提取测试工具")
    print("=" * 80)
    
    # 扫描可用文件
    pdf_folder = Path("PDF")
    if not pdf_folder.exists():
        print("❌ PDF文件夹不存在")
        return
    
    # 获取所有支持的文件
    files = []
    for pattern in ['*.pdf', '*.docx']:
        files.extend(list(pdf_folder.glob(pattern)))
    
    if not files:
        print("❌ 没有找到支持的文件")
        return
    
    print(f"📁 找到 {len(files)} 个文件:")
    for i, file_path in enumerate(files, 1):
        print(f"  {i}. {file_path.name}")
    
    print()
    
    # 用户选择文件
    try:
        choice = input("请选择要测试的文件编号 (1-{}): ".format(len(files)))
        file_index = int(choice) - 1
        
        if file_index < 0 or file_index >= len(files):
            print("❌ 无效的文件编号")
            return
        
        selected_file = str(files[file_index])
        print(f"\n✅ 选择文件: {selected_file}")
        print()
        
        # 执行测试
        test_raw_extraction(selected_file)
        test_technical_extraction(selected_file)
        test_ab_detection(selected_file)
        
    except KeyboardInterrupt:
        print("\n👋 测试已取消")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
