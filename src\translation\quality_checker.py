#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译质量检查器
统一的翻译质量检查和验证逻辑
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class QualityCheckResult:
    """质量检查结果"""
    is_valid: bool
    confidence_score: float
    chinese_remaining_ratio: float
    issues: List[str]
    suggestions: List[str]


class QualityChecker:
    """
    翻译质量检查器
    
    提供统一的翻译质量检查功能：
    1. 中文残留检测
    2. 翻译完整性验证
    3. 格式问题检查
    4. 置信度评分
    5. 改进建议生成
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 质量检查配置
        self.max_chinese_remaining_ratio = 0.3  # 最大中文残留比例
        self.min_translation_ratio = 0.3        # 最小翻译比例
        self.max_length_expansion = 3.0         # 最大长度扩展比例
        
        # 错误模式
        self.error_patterns = [
            (r'\d+\d+(?:mPa|g/cm|℃)', "数字连接错误"),
            (r'\d+\s+\d+\s+\d+', "数字异常分割"),
            (r'[A-Za-z]{25,}', "英文单词过长"),
            (r'\d+times', "指数翻译错误"),
            (r'(\d+)\s*(\d+)\s*(mPa|g/cm|℃)', "单位格式错误"),
        ]
        
        # 质量指标权重
        self.weights = {
            'chinese_remaining': 0.4,
            'format_issues': 0.3,
            'length_ratio': 0.2,
            'completeness': 0.1
        }
    
    def check_translation_quality(self, original: str, translated: str) -> QualityCheckResult:
        """
        检查翻译质量
        
        Args:
            original: 原文
            translated: 译文
            
        Returns:
            QualityCheckResult: 质量检查结果
        """
        if not translated or not translated.strip():
            return QualityCheckResult(
                is_valid=False,
                confidence_score=0.0,
                chinese_remaining_ratio=1.0,
                issues=["翻译结果为空"],
                suggestions=["重新翻译"]
            )
        
        # 如果翻译结果和原文相同，认为翻译失败
        if translated.strip() == original.strip():
            return QualityCheckResult(
                is_valid=False,
                confidence_score=0.0,
                chinese_remaining_ratio=1.0,
                issues=["翻译结果与原文相同"],
                suggestions=["使用不同的翻译方法"]
            )
        
        issues = []
        suggestions = []
        
        # 1. 检查中文残留
        chinese_remaining_ratio = self._check_chinese_remaining(original, translated)
        if chinese_remaining_ratio > self.max_chinese_remaining_ratio:
            issues.append(f"中文残留过多: {chinese_remaining_ratio:.1%}")
            suggestions.append("进一步细分文本重新翻译")
        
        # 2. 检查格式问题
        format_issues = self._check_format_issues(translated)
        issues.extend(format_issues)
        
        # 3. 检查长度比例
        length_ratio = len(translated) / len(original) if original else 1
        if length_ratio > self.max_length_expansion:
            issues.append(f"翻译长度过长: {length_ratio:.1f}倍")
            suggestions.append("检查翻译是否有重复或冗余")
        elif length_ratio < self.min_translation_ratio:
            issues.append(f"翻译长度过短: {length_ratio:.1f}倍")
            suggestions.append("检查翻译是否完整")
        
        # 4. 计算置信度分数
        confidence_score = self._calculate_confidence_score(
            original, translated, chinese_remaining_ratio, format_issues, length_ratio
        )
        
        # 5. 判断是否有效
        is_valid = (
            chinese_remaining_ratio <= self.max_chinese_remaining_ratio and
            len(format_issues) == 0 and
            self.min_translation_ratio <= length_ratio <= self.max_length_expansion
        )
        
        return QualityCheckResult(
            is_valid=is_valid,
            confidence_score=confidence_score,
            chinese_remaining_ratio=chinese_remaining_ratio,
            issues=issues,
            suggestions=suggestions
        )
    
    def _check_chinese_remaining(self, original: str, translated: str) -> float:
        """检查中文残留比例"""
        # 统计原文中的中文字符
        original_chinese = re.findall(r'[\u4e00-\u9fff]', original)
        translated_chinese = re.findall(r'[\u4e00-\u9fff]', translated)
        
        if not original_chinese:
            return 0.0
        
        # 计算残留比例
        remaining_ratio = len(translated_chinese) / len(original_chinese)
        return min(1.0, remaining_ratio)
    
    def _check_format_issues(self, translated: str) -> List[str]:
        """检查格式问题"""
        issues = []
        
        for pattern, description in self.error_patterns:
            if re.search(pattern, translated):
                issues.append(description)
        
        return issues
    
    def _calculate_confidence_score(self, original: str, translated: str, 
                                  chinese_remaining_ratio: float, 
                                  format_issues: List[str], 
                                  length_ratio: float) -> float:
        """计算置信度分数"""
        score = 1.0
        
        # 中文残留扣分
        chinese_penalty = chinese_remaining_ratio * self.weights['chinese_remaining']
        score -= chinese_penalty
        
        # 格式问题扣分
        format_penalty = len(format_issues) * 0.1 * self.weights['format_issues']
        score -= format_penalty
        
        # 长度比例扣分
        if length_ratio > self.max_length_expansion or length_ratio < self.min_translation_ratio:
            length_penalty = 0.3 * self.weights['length_ratio']
            score -= length_penalty
        
        # 完整性检查
        if self._is_incomplete_translation(original, translated):
            completeness_penalty = 0.5 * self.weights['completeness']
            score -= completeness_penalty
        
        return max(0.0, min(1.0, score))
    
    def _is_incomplete_translation(self, original: str, translated: str) -> bool:
        """检查翻译是否不完整"""
        # 检查是否有明显的截断
        if translated.endswith('...') or translated.endswith('…'):
            return True
        
        # 检查是否有未完成的句子
        if re.search(r'[A-Za-z]+\s*$', translated) and not re.search(r'[.!?]$', translated):
            return True
        
        # 检查数值是否完整
        original_numbers = re.findall(r'\d+(?:\.\d+)?', original)
        translated_numbers = re.findall(r'\d+(?:\.\d+)?', translated)
        
        if len(original_numbers) > len(translated_numbers):
            return True
        
        return False
    
    def batch_check_quality(self, translation_pairs: List[Tuple[str, str]]) -> List[QualityCheckResult]:
        """批量质量检查"""
        results = []
        
        for original, translated in translation_pairs:
            result = self.check_translation_quality(original, translated)
            results.append(result)
        
        return results
    
    def get_quality_summary(self, results: List[QualityCheckResult]) -> Dict[str, float]:
        """获取质量汇总统计"""
        if not results:
            return {}
        
        valid_count = sum(1 for r in results if r.is_valid)
        avg_confidence = sum(r.confidence_score for r in results) / len(results)
        avg_chinese_remaining = sum(r.chinese_remaining_ratio for r in results) / len(results)
        
        # 统计常见问题
        all_issues = []
        for result in results:
            all_issues.extend(result.issues)
        
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        return {
            'total_translations': len(results),
            'valid_translations': valid_count,
            'valid_rate': valid_count / len(results),
            'avg_confidence': avg_confidence,
            'avg_chinese_remaining': avg_chinese_remaining,
            'common_issues': dict(sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:5])
        }
    
    def suggest_improvements(self, results: List[QualityCheckResult]) -> List[str]:
        """基于质量检查结果提供改进建议"""
        suggestions = []
        
        # 分析常见问题
        all_issues = []
        all_suggestions = []
        
        for result in results:
            all_issues.extend(result.issues)
            all_suggestions.extend(result.suggestions)
        
        # 统计问题频率
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        # 生成改进建议
        if any("中文残留" in issue for issue in all_issues):
            suggestions.append("建议优化分段策略，将长文本分割成更小的片段")
        
        if any("格式错误" in issue for issue in all_issues):
            suggestions.append("建议增强数值和单位的保护机制")
        
        if any("长度过长" in issue for issue in all_issues):
            suggestions.append("建议检查翻译模型的参数设置，避免过度翻译")
        
        # 去重并返回
        return list(set(suggestions))
    
    def update_thresholds(self, chinese_remaining_threshold: Optional[float] = None,
                         length_expansion_threshold: Optional[float] = None):
        """更新质量检查阈值"""
        if chinese_remaining_threshold is not None:
            self.max_chinese_remaining_ratio = chinese_remaining_threshold
            self.logger.info(f"中文残留阈值更新为: {chinese_remaining_threshold}")
        
        if length_expansion_threshold is not None:
            self.max_length_expansion = length_expansion_threshold
            self.logger.info(f"长度扩展阈值更新为: {length_expansion_threshold}")
