#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能特点提取功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger
from src.summary_table import ConfigBasedExtractor, SummaryTableConfig

logger = get_logger(__name__)


def test_performance_features_extraction():
    """测试性能特点提取功能"""
    print("🧪 测试性能特点提取功能")
    print("=" * 60)
    
    try:
        # 初始化提取器
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        
        # 扫描文件
        test_folder = "PDF"
        if not os.path.exists(test_folder):
            print(f"❌ 测试文件夹不存在: {test_folder}")
            return False
        
        files = extractor.scan_folder(test_folder)
        print(f"📁 找到 {len(files)} 个支持的文件")
        
        if not files:
            print("⚠️ 没有找到测试文件")
            return True
        
        # 测试前几个文件
        success_count = 0
        total_count = 0
        
        for i, file_path in enumerate(files[:5]):  # 测试前5个文件
            filename = os.path.basename(file_path)
            print(f"\n📄 测试文件 {i+1}: {filename}")
            
            try:
                result = extractor.extract_from_file(file_path)
                total_count += 1
                
                if result and '性能特点' in result['parameters']:
                    performance_features = result['parameters']['性能特点']
                    print(f"   ✅ 成功提取性能特点:")
                    print(f"   📝 内容: {performance_features[:100]}...")
                    if len(performance_features) > 100:
                        print(f"   📏 总长度: {len(performance_features)} 字符")
                    success_count += 1
                else:
                    print(f"   ⚠️ 未找到性能特点")
                    
                    # 显示其他提取到的参数
                    if result and result['parameters']:
                        print(f"   📋 其他参数: {list(result['parameters'].keys())[:5]}...")
                
            except Exception as e:
                print(f"   ❌ 提取失败: {e}")
                total_count += 1
        
        print(f"\n📊 测试结果:")
        print(f"   总文件数: {total_count}")
        print(f"   成功提取性能特点: {success_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%" if total_count > 0 else "   成功率: 0%")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_specific_file_performance_features(file_path: str):
    """测试特定文件的性能特点提取"""
    print(f"\n🔍 详细测试文件: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        
        # 获取原始内容
        from src.extraction import PDFExtractor, WordExtractor
        
        if file_path.endswith('.pdf'):
            content_extractor = PDFExtractor()
        else:
            content_extractor = WordExtractor()
        
        content = content_extractor.extract_content(file_path)
        
        if content:
            print(f"📄 文档信息:")
            print(f"   标题: {content.title}")
            print(f"   表格数: {len(content.tables) if content.tables else 0}")
            print(f"   段落数: {len(content.paragraphs) if content.paragraphs else 0}")
            
            # 显示前几个段落
            if content.paragraphs:
                print(f"\n📝 前几个段落:")
                for i, paragraph in enumerate(content.paragraphs[:5]):
                    if paragraph.strip():
                        print(f"   段落{i+1}: {paragraph[:80]}...")
            
            # 测试性能特点提取
            performance_features = extractor._extract_performance_features(content)
            
            if performance_features:
                print(f"\n✅ 性能特点提取成功:")
                print(f"📝 完整内容: {performance_features}")
            else:
                print(f"\n⚠️ 未提取到性能特点")
        
        return True
        
    except Exception as e:
        print(f"❌ 详细测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 性能特点提取功能测试")
    print("=" * 60)
    
    # 基础测试
    success = test_performance_features_extraction()
    
    if success:
        print("\n🎉 基础测试通过！")
        
        # 选择一个文件进行详细测试
        test_folder = "PDF"
        if os.path.exists(test_folder):
            files = []
            for ext in ["*.pdf", "*.docx"]:
                files.extend(list(Path(test_folder).glob(ext)))
            
            if files:
                # 选择第一个PDF文件进行详细测试
                pdf_files = [f for f in files if str(f).endswith('.pdf')]
                if pdf_files:
                    test_specific_file_performance_features(str(pdf_files[0]))
    else:
        print("\n⚠️ 基础测试失败，需要进一步调试。")


if __name__ == "__main__":
    main()
