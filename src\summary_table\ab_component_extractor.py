#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A/B组分提取器
专门处理A/B组分技术参数的识别和提取
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from ..utils import get_logger

logger = get_logger(__name__)


class ABComponentExtractor:
    """A/B组分提取器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # A/B组分的核心参数（按置信度排序）
        self.core_ab_parameters = {
            '外观': ['外观', '外观状态', '外观性状', '外观形态'],  # 置信度最高
            '粘度': ['粘度', '黏度', '粘度值', '黏度值', '粘度(mPa·s)', '粘度(Pa·s)'],  # 置信度高
            '密度': ['密度', '密度值', '比重', '密度(g/cm³)', '密度(g/ml)'],  # 置信度高
        }

        # 可选参数（可能存在，但不是必须的）
        self.optional_ab_parameters = {
            '保存期限': ['保存期限', '储存期限', '保质期', '有效期', '贮存期'],
        }
        
        # 表格定位关键词
        self.section_keywords = ['常规性能', '液体性能', '基础性能', '组分性能']
        
        self.logger.info("A/B组分提取器初始化完成")
    
    def extract_ab_components(self, content: Dict[str, Any]) -> Dict[str, str]:
        """提取A/B组分参数"""
        try:
            # 查找A/B组分表格
            ab_table = self._find_ab_component_table(content)
            
            if not ab_table:
                self.logger.debug("未找到A/B组分表格")
                return {}
            
            # 提取A/B组分数据
            ab_data = self._extract_ab_data_from_table(ab_table)
            
            self.logger.info(f"A/B组分提取完成，提取到 {len(ab_data)} 个参数")
            return ab_data
            
        except Exception as e:
            self.logger.error(f"A/B组分提取失败: {e}")
            return {}
    
    def _find_ab_component_table(self, content: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """查找A/B组分表格（包括嵌套表格）"""
        tables = content.get('tables', [])

        # 优先查找嵌套表格（通常A/B组分数据在嵌套表格中）
        for table in tables:
            # 检查是否为嵌套表格且包含A/B组分
            if self._is_nested_ab_table(table):
                self.logger.debug(f"找到嵌套A/B组分表格，页面: {table.get('page', 'N/A')}")
                return table

        # 如果没有找到嵌套表格，查找普通表格
        for table in tables:
            # 检查是否为A/B组分表格
            if self._is_ab_component_table(table):
                self.logger.debug(f"找到A/B组分表格，页面: {table.get('page', 'N/A')}")
                return table

        return None

    def _is_nested_ab_table(self, table: Dict[str, Any]) -> bool:
        """判断是否为包含A/B组分的嵌套表格"""
        # 检查表格位置信息，看是否为嵌套表格
        position = table.get('position', {})
        if not position.get('nested', False):
            return False

        # 检查父单元格内容是否包含液体性能、常规性能等关键词
        parent_cell = position.get('parent_cell', '')
        if any(keyword in parent_cell for keyword in self.section_keywords):
            # 进一步检查是否为A/B组分表格
            return self._is_ab_component_table(table)

        return False
    
    def _is_ab_component_table(self, table: Dict[str, Any]) -> bool:
        """判断是否为A/B组分表格（仅限液体性能相关表格）"""
        table_data = table.get('data', [])
        if not table_data:
            return False

        # 检查1：表格必须有足够的列（至少3列：项目、A组分、B组分）
        if not any(len(row) >= 3 for row in table_data):
            return False

        # 检查2：查找A/B列标题
        ab_columns = self._find_ab_columns(table_data)
        if not ab_columns:
            return False

        # 检查3：必须是液体性能相关的表格
        # 通过检查表格上下文或第一列内容来判断
        if not self._is_liquid_performance_table(table_data):
            return False

        # 检查4：第一列必须包含核心A/B参数
        first_column_items = [row[0].strip() for row in table_data if row and row[0].strip()]
        core_params_found = 0
        found_appearance = False  # 外观参数的特殊标记

        # 检查核心参数
        for param_name, param_group in self.core_ab_parameters.items():
            for item in first_column_items:
                if any(param in item for param in param_group):
                    core_params_found += 1
                    if param_name == '外观':
                        found_appearance = True
                    self.logger.debug(f"找到核心参数: {param_name} 在 '{item}'")
                    break

        # 检查可选参数
        optional_params_found = 0
        for param_group in self.optional_ab_parameters.values():
            for item in first_column_items:
                if any(param in item for param in param_group):
                    optional_params_found += 1
                    break

        # 判断逻辑：
        # 1. 如果找到外观参数，只需要再找到1个其他核心参数即可
        # 2. 如果没有外观参数，需要找到至少2个核心参数
        if found_appearance:
            is_ab_table = core_params_found >= 2  # 外观 + 至少1个其他参数
        else:
            is_ab_table = core_params_found >= 2  # 至少2个核心参数

        if is_ab_table:
            self.logger.debug(f"A/B组分表格验证通过，找到 {core_params_found} 个核心参数，{optional_params_found} 个可选参数，外观参数: {'是' if found_appearance else '否'}")

        return is_ab_table

    def _is_liquid_performance_table(self, table_data: List[List[str]]) -> bool:
        """判断是否为液体性能相关表格"""
        # 检查表格中是否包含液体性能的特征参数
        liquid_indicators = ['外观', '粘度', '密度', '保存期限']

        first_column_items = [row[0].strip() for row in table_data if row and row[0].strip()]

        # 至少要包含2个液体性能指标
        found_indicators = 0
        for item in first_column_items:
            for indicator in liquid_indicators:
                if indicator in item:
                    found_indicators += 1
                    break

        return found_indicators >= 2
    
    def _find_ab_columns(self, table_data: List[List[str]]) -> Optional[Tuple[int, int]]:
        """查找A和B组分的列索引"""
        # 检查前几行，寻找包含A/B标识的列标题
        for row_idx, row in enumerate(table_data[:3]):  # 只检查前3行
            if len(row) < 3:
                continue

            a_candidates = []
            b_candidates = []

            for col_idx, cell in enumerate(row[1:], 1):  # 跳过第一列（项目列）
                cell_text = cell.strip()

                # 检查A组分标识
                if self._is_a_component_column(cell_text):
                    a_candidates.append(col_idx)

                # 检查B组分标识
                elif self._is_b_component_column(cell_text):
                    b_candidates.append(col_idx)

            # 验证A/B列的位置关系：A列号应该小于B列号
            for a_col in a_candidates:
                for b_col in b_candidates:
                    if a_col < b_col:  # A在B的左边
                        self.logger.debug(f"找到有效A/B列: A列={a_col}('{row[a_col]}'), B列={b_col}('{row[b_col]}')")
                        return (a_col, b_col)

            # 如果找到了候选列但位置关系不对，记录警告
            if a_candidates and b_candidates:
                self.logger.debug(f"找到A/B候选列但位置关系不符合要求: A列={a_candidates}, B列={b_candidates}")

        return None
    
    def _is_a_component_column(self, text: str) -> bool:
        """判断是否为A组分列"""
        import re

        # 排除括号内的A/B，避免误识别产品名称
        if '(' in text and ')' in text:
            # 检查A是否在括号内
            bracket_content = re.findall(r'\([^)]*A[^)]*\)', text)
            if bracket_content:
                # A在括号内，不认为是A组分列
                return False

        # A组分的各种表示方式（更严格的匹配）
        a_patterns = [
            r'^.*A$',                    # 以A结尾，如 109-17A, FM-700A
            r'^A$',                      # 单独的A
            r'^A组[分份]$',              # A组分、A组份
            r'^A剂$',                    # A剂
            r'^主剂$',                   # 主剂
            r'^A\s*COMPONENT$',          # A COMPONENT
            r'^A料$',                    # A料
            r'.*[^(]A$',                 # 不在括号内且以A结尾
        ]

        for pattern in a_patterns:
            if re.search(pattern, text.strip(), re.IGNORECASE):
                self.logger.debug(f"A组分列匹配: '{text}' -> 模式: {pattern}")
                return True

        return False

    def _is_b_component_column(self, text: str) -> bool:
        """判断是否为B组分列"""
        import re

        # 排除括号内的A/B，避免误识别产品名称
        if '(' in text and ')' in text:
            # 检查B是否在括号内
            bracket_content = re.findall(r'\([^)]*B[^)]*\)', text)
            if bracket_content:
                # B在括号内，不认为是B组分列
                return False

        # B组分的各种表示方式（更严格的匹配）
        b_patterns = [
            r'^.*B$',                    # 以B结尾，如 109-17B, FM-700B
            r'^.*B-\d+$',                # 以B-数字结尾，如 FM-700B-2
            r'^B$',                      # 单独的B
            r'^B组[分份]$',              # B组分、B组份
            r'^B剂$',                    # B剂
            r'^固化剂$',                 # 固化剂
            r'^B\s*COMPONENT$',          # B COMPONENT
            r'^B料$',                    # B料
            r'.*[^(]B$',                 # 不在括号内且以B结尾
            r'.*[^(]B-\d+$',             # 不在括号内且以B-数字结尾
        ]

        for pattern in b_patterns:
            if re.search(pattern, text.strip(), re.IGNORECASE):
                self.logger.debug(f"B组分列匹配: '{text}' -> 模式: {pattern}")
                return True

        return False
    
    def _extract_ab_data_from_table(self, table: Dict[str, Any]) -> Dict[str, str]:
        """从表格中提取A/B组分数据"""
        table_data = table.get('data', [])
        ab_columns = self._find_ab_columns(table_data)
        
        if not ab_columns:
            return {}
        
        a_col, b_col = ab_columns
        extracted_data = {}
        
        # 合并所有参数（核心 + 可选）
        all_parameters = {**self.core_ab_parameters, **self.optional_ab_parameters}

        # 遍历表格行，提取参数
        for row in table_data:
            if len(row) <= max(a_col, b_col):
                continue

            item_name = row[0].strip()
            if not item_name:
                continue

            # 跳过表头行
            if item_name in ['测试项目', '项目', 'Item', 'Parameter']:
                continue

            # 匹配所有参数
            matched = False
            for standard_param, aliases in all_parameters.items():
                if any(alias in item_name for alias in aliases):
                    # 检查是否已经提取过这个参数，避免重复覆盖
                    a_key = f"{standard_param}(A)"
                    b_key = f"{standard_param}(B)"

                    # 提取A组分数据
                    if a_col < len(row) and a_key not in extracted_data:
                        a_value = row[a_col].strip()
                        self.logger.debug(f"检查A组分数据: '{item_name}' -> 列{a_col} = '{a_value}', 是否为单位: {self._is_unit_or_condition(a_value)}")
                        if a_value and not self._is_unit_or_condition(a_value):
                            extracted_data[a_key] = a_value
                            self.logger.info(f"✓ 提取A组分: {a_key} = {a_value}")
                    elif a_key in extracted_data:
                        self.logger.debug(f"跳过重复A组分: {a_key} (已存在: {extracted_data[a_key]})")

                    # 提取B组分数据
                    if b_col < len(row) and b_key not in extracted_data:
                        b_value = row[b_col].strip()
                        self.logger.debug(f"检查B组分数据: '{item_name}' -> 列{b_col} = '{b_value}', 是否为单位: {self._is_unit_or_condition(b_value)}")
                        if b_value and not self._is_unit_or_condition(b_value):
                            extracted_data[b_key] = b_value
                            self.logger.info(f"✓ 提取B组分: {b_key} = {b_value}")
                    elif b_key in extracted_data:
                        self.logger.debug(f"跳过重复B组分: {b_key} (已存在: {extracted_data[b_key]})")

                    matched = True
                    break

            # 如果没有匹配到标准参数，记录调试信息
            if not matched:
                self.logger.debug(f"未匹配参数: '{item_name}' -> A: '{row[a_col] if a_col < len(row) else 'N/A'}', B: '{row[b_col] if b_col < len(row) else 'N/A'}')")
        
        return extracted_data

    def _is_unit_or_condition(self, text: str) -> bool:
        """判断文本是否为单位或测试条件"""
        if not text:
            return True

        # 如果包含数值，很可能是实际数据而不是单位
        import re
        if re.search(r'\d+[±\-\+]?\d*', text):
            # 包含数字，检查是否为数值数据
            if any(pattern in text for pattern in ['±', '>', '<', '≥', '≤']):
                return False  # 包含数值范围符号，是数据
            if re.match(r'^\d+(\.\d+)?[±\-\+]\d+(\.\d+)?$', text):
                return False  # 纯数值格式，如 400±200

        # 常见的单位和条件标识
        unit_indicators = [
            'mPa·s', 'Pa·s', 'g/cm3', 'g/ml', 'kg/m3',
            'Shore A', 'Shore D', 'MPa', 'KV/mm', 'Ω.cm',
            '℃', '°C', 'min', 'h', 'hr', '%', 'mm',
            '目测', '室温', '密封', '常温', '常湿',
            '23℃', '25℃', '60℃', '85℃',
            'GB/T', 'ASTM', 'ISO', '企业标准',
            '单位或条件', '测试条件', '执行标准'
        ]

        # 检查是否包含单位标识
        for indicator in unit_indicators:
            if indicator in text:
                return True

        # 检查是否为表头标识
        header_indicators = ['109-17A', '109-17B', '测试项目', '单位或条件', '执行标准']
        if text in header_indicators:
            return True

        return False

    def is_ab_product(self, content: Dict[str, Any], product_name: str = "") -> bool:
        """判断是否为A/B组分产品"""
        # 方法1：检查产品名称
        ab_indicators = ["双组分", "双组份", "A/B", "A+B", "两组分", "两组份", "双液", "二液"]
        if any(indicator in product_name for indicator in ab_indicators):
            return True
        
        # 方法2：检查是否能找到A/B组分表格
        ab_table = self._find_ab_component_table(content)
        return ab_table is not None
    
    def get_extraction_summary(self, extracted_data: Dict[str, str]) -> Dict[str, Any]:
        """获取提取摘要"""
        a_params = {k: v for k, v in extracted_data.items() if k.endswith('(A)')}
        b_params = {k: v for k, v in extracted_data.items() if k.endswith('(B)')}
        
        return {
            'total_ab_parameters': len(extracted_data),
            'a_component_parameters': len(a_params),
            'b_component_parameters': len(b_params),
            'core_parameters_found': [
                param.replace('(A)', '').replace('(B)', '') 
                for param in extracted_data.keys()
            ],
            'extraction_complete': len(a_params) >= 2 and len(b_params) >= 2
        }
