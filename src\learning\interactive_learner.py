#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式学习器
提供智能的词汇级别学习功能
"""

from typing import List, Dict, Optional
from ..database import TranslationDatabase
from ..database.models import TranslationTerm, TermType, TranslationSource
from .term_extractor import TermExtractor, TermCandidate
from .suggestion_engine import SuggestionEngine, TranslationSuggestion

class InteractiveLearner:
    """交互式学习器"""
    
    def __init__(self, db: TranslationDatabase):
        self.db = db
        self.term_extractor = TermExtractor()
        self.suggestion_engine = SuggestionEngine()
    
    def analyze_translation_result(self, original: str, translated: str,
                                 translation_method: str) -> Dict:
        """分析翻译结果，提取学习机会"""
        analysis = {
            'learnable_terms': [],
            'suggestions': {},
            'quality_issues': [],
            'recommendations': []
        }

        # 检查输入有效性
        if not original or not translated:
            analysis['quality_issues'].append("翻译输入或输出为空")
            return analysis

        # 提取可学习的术语
        try:
            if translation_method == 'ai':
                candidates = self.term_extractor.extract_from_ai_translation(original, translated)
            elif 'partial' in translation_method or '部分' in translation_method:
                candidates = self.term_extractor.extract_from_partial_translation(original, translated)
            else:
                candidates = []
        except Exception as e:
            analysis['quality_issues'].append(f"术语提取失败: {e}")
            candidates = []
        
        # 过滤高质量候选
        high_quality_candidates = [
            c for c in candidates 
            if c.confidence > 0.6 and len(c.chinese) >= 2
        ]
        
        analysis['learnable_terms'] = high_quality_candidates
        
        # 为每个候选术语生成建议
        for candidate in high_quality_candidates:
            suggestions = self.suggestion_engine.generate_suggestions(
                candidate.chinese, 
                candidate.context
            )
            analysis['suggestions'][candidate.chinese] = suggestions
        
        # 质量问题检测
        analysis['quality_issues'] = self._detect_quality_issues(original, translated)
        
        # 生成改进建议
        analysis['recommendations'] = self._generate_recommendations(analysis)
        
        return analysis
    
    def present_learning_options(self, analysis: Dict) -> List[Dict]:
        """呈现学习选项给用户"""
        learning_options = []
        
        for candidate in analysis['learnable_terms']:
            chinese_term = candidate.chinese
            suggestions = analysis['suggestions'].get(chinese_term, [])
            
            if not suggestions:
                continue
            
            option = {
                'chinese': chinese_term,
                'context': candidate.context[:50] + '...',
                'term_type': candidate.term_type,
                'confidence': candidate.confidence,
                'suggestions': [
                    {
                        'english': s.english,
                        'confidence': s.confidence,
                        'explanation': s.explanation,
                        'source': s.source
                    }
                    for s in suggestions[:3]  # 只显示前3个建议
                ],
                'current_translation': candidate.english if candidate.english else "未翻译"
            }
            
            learning_options.append(option)
        
        # 按置信度排序
        learning_options.sort(key=lambda x: x['confidence'], reverse=True)
        
        return learning_options[:10]  # 最多返回10个学习选项
    
    def apply_user_selection(self, chinese: str, selected_english: str, 
                           term_type: str = "basic", confidence: float = 1.0) -> bool:
        """应用用户选择的翻译"""
        try:
            # 转换术语类型
            term_type_enum = TermType(term_type)
            
            # 创建术语对象
            term = TranslationTerm(
                chinese=chinese,
                english=selected_english,
                term_type=term_type_enum,
                source=TranslationSource.USER_FEEDBACK,
                confidence=confidence,
                context="用户交互学习",
                notes=f"通过交互式学习添加，置信度: {confidence}"
            )
            
            # 添加到数据库
            term_id = self.db.add_term(term)
            
            if term_id:
                print(f"✅ 学习成功: '{chinese}' → '{selected_english}'")
                return True
            else:
                print(f"❌ 学习失败: '{chinese}'")
                return False
                
        except Exception as e:
            print(f"❌ 学习过程出错: {e}")
            return False
    
    def batch_apply_selections(self, selections: List[Dict]) -> Dict[str, bool]:
        """批量应用用户选择"""
        results = {}
        
        for selection in selections:
            chinese = selection['chinese']
            english = selection['english']
            term_type = selection.get('term_type', 'basic')
            confidence = selection.get('confidence', 1.0)
            
            success = self.apply_user_selection(chinese, english, term_type, confidence)
            results[chinese] = success
        
        return results
    
    def _detect_quality_issues(self, original: str, translated: str) -> List[str]:
        """检测翻译质量问题"""
        issues = []
        
        # 检查未翻译的中文
        import re
        remaining_chinese = re.findall(r'[\u4e00-\u9fa5]+', translated)
        if remaining_chinese:
            issues.append(f"包含未翻译的中文: {remaining_chinese[:5]}")
        
        # 检查英中混杂
        mixed_patterns = [
            r'[a-zA-Z]+[\u4e00-\u9fa5]+',
            r'[\u4e00-\u9fa5]+[a-zA-Z]+[\u4e00-\u9fa5]+'
        ]
        
        for pattern in mixed_patterns:
            if re.search(pattern, translated):
                issues.append("存在英中混杂问题")
                break
        
        # 检查翻译长度异常
        if len(translated) > len(original) * 3:
            issues.append("翻译结果异常冗长")
        elif len(translated) < len(original) * 0.3:
            issues.append("翻译结果异常简短")
        
        return issues
    
    def _generate_recommendations(self, analysis: Dict) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        learnable_count = len(analysis['learnable_terms'])
        if learnable_count > 0:
            recommendations.append(f"发现 {learnable_count} 个可学习术语，建议逐个确认")
        
        quality_issues = analysis['quality_issues']
        if quality_issues:
            recommendations.append("存在翻译质量问题，建议优先解决高频术语")
        
        # 基于术语类型的建议
        term_types = {}
        for candidate in analysis['learnable_terms']:
            term_type = candidate.term_type
            term_types[term_type] = term_types.get(term_type, 0) + 1
        
        if term_types:
            most_common_type = max(term_types, key=term_types.get)
            recommendations.append(f"主要缺失 {most_common_type} 类术语，建议重点补充")
        
        return recommendations
    
    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        stats = self.db.get_statistics()
        
        # 添加学习相关的统计
        with self.db._get_connection() as conn:
            # 用户反馈术语数量
            user_feedback_count = conn.execute("""
                SELECT COUNT(*) FROM translation_terms 
                WHERE source = 'user_feedback' AND is_active = 1
            """).fetchone()[0]
            
            # 最近学习的术语
            recent_terms = conn.execute("""
                SELECT chinese, english, created_at FROM translation_terms 
                WHERE source = 'user_feedback' AND is_active = 1
                ORDER BY created_at DESC LIMIT 5
            """).fetchall()
        
        stats['user_learned_terms'] = user_feedback_count
        stats['recent_learned'] = [
            {'chinese': row['chinese'], 'english': row['english'], 'time': row['created_at']}
            for row in recent_terms
        ]
        
        return stats
