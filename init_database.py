#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
将现有的词典数据迁移到SQLite数据库
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """主函数"""
    print("🔄 开始初始化翻译数据库...")
    
    try:
        from src.database import TranslationDatabase
        from src.database.migration import DataMigration
        
        # 创建数据库
        db = TranslationDatabase()
        print("✅ 数据库创建成功")
        
        # 执行数据迁移
        migration = DataMigration(db)
        
        print("🔄 开始迁移词典数据...")
        if migration.migrate_from_dictionary_data():
            print("✅ 词典数据迁移成功")
            
            # 验证迁移结果
            print("🔍 验证迁移结果...")
            if migration.validate_migration():
                print("✅ 迁移验证通过")
            else:
                print("❌ 迁移验证失败")
                return False
            
            # 显示统计信息
            stats = db.get_statistics()
            print("\n📊 数据库统计信息:")
            print(f"  术语总数: {stats['total_terms']}")
            print(f"  智能模式: {stats['total_patterns']}")
            print(f"  翻译记录: {stats['total_translations']}")
            print(f"  待学习记录: {stats['pending_learning']}")
            
            # 导出备份
            print("\n💾 导出数据备份...")
            migration.export_to_files()
            print("✅ 备份导出完成")
            
            print("\n🎉 数据库初始化完成！")
            print("现在可以使用基于数据库的翻译系统了。")
            
            return True
            
        else:
            print("❌ 数据迁移失败")
            return False
            
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
