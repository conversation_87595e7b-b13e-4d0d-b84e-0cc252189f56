#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的数据提取架构
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger
from src.summary_table import ConfigBasedExtractor, SummaryTableConfig

logger = get_logger(__name__)


def test_config_based_extractor():
    """测试配置驱动的提取器"""
    print("=" * 60)
    print("🧪 测试配置驱动的数据提取器")
    print("=" * 60)
    
    try:
        # 初始化配置和提取器
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        
        # 测试文件夹扫描
        test_folder = "PDF"
        print(f"📁 扫描测试文件夹: {test_folder}")
        
        if not os.path.exists(test_folder):
            print(f"❌ 测试文件夹不存在: {test_folder}")
            return False
        
        files = extractor.scan_folder(test_folder)
        print(f"✅ 找到 {len(files)} 个支持的文件")
        
        if not files:
            print("⚠️ 没有找到测试文件")
            return True
        
        # 测试单个文件提取
        test_file = files[0]  # 取第一个文件进行测试
        print(f"\n📄 测试文件提取: {os.path.basename(test_file)}")
        
        result = extractor.extract_from_file(test_file)
        
        if result:
            print("✅ 文件提取成功")
            print(f"   产品名称: {result.get('product_name', 'N/A')}")
            print(f"   产品型号: {result.get('product_model', 'N/A')}")
            print(f"   参数数量: {len(result.get('parameters', {}))}")
            
            # 显示前几个参数作为示例
            parameters = result.get('parameters', {})
            if parameters:
                print("   参数示例:")
                for i, (key, value) in enumerate(list(parameters.items())[:5]):
                    print(f"     {key}: {value}")
                if len(parameters) > 5:
                    print(f"     ... 还有 {len(parameters) - 5} 个参数")
            
            return True
        else:
            print("❌ 文件提取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_parameter_mapping():
    """测试参数映射功能"""
    print("\n" + "=" * 60)
    print("🧪 测试参数映射功能")
    print("=" * 60)
    
    try:
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        
        # 测试参数映射
        test_params = {
            "外观": "透明液体",
            "粘度": "1000 mPa·s",
            "密度": "1.2 g/cm³",
            "混合比例": "1:1",
            "固化时间": "24小时",
            "拉伸强度": "25 MPa"
        }
        
        print("📋 测试原始参数:")
        for key, value in test_params.items():
            print(f"   {key}: {value}")
        
        # 测试匹配
        matched = extractor._match_parameters(test_params)
        
        print("\n✅ 匹配结果:")
        for key, value in matched.items():
            print(f"   {key}: {value}")
        
        # 测试AB组件处理
        ab_test_params = {
            "外观": "A组分: 透明液体, B组分: 黑色液体",
            "粘度": "A组分: 1000 mPa·s, B组分: 500 mPa·s"
        }
        
        print(f"\n📋 测试AB组件参数:")
        for key, value in ab_test_params.items():
            print(f"   {key}: {value}")
        
        processed = extractor._process_ab_components(ab_test_params, "FM-102双组分胶")
        
        print("\n✅ AB组件处理结果:")
        for key, value in processed.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数映射测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n" + "=" * 60)
    print("🧪 测试配置加载")
    print("=" * 60)
    
    try:
        config = SummaryTableConfig()
        
        # 测试参数映射加载
        param_mapping = config.get_param_mapping()
        print(f"✅ 参数映射加载成功，包含 {len(param_mapping)} 个标准参数")
        
        # 显示几个示例
        print("   参数映射示例:")
        for i, (key, aliases) in enumerate(list(param_mapping.items())[:3]):
            print(f"     {key}: {aliases}")
        
        # 测试列映射加载
        column_mapping = config.get_column_mapping()
        print(f"✅ 列映射加载成功，包含 {len(column_mapping)} 个列")
        
        # 测试AB参数加载
        ab_params = config.get_ab_parameters()
        print(f"✅ AB参数加载成功，包含 {len(ab_params)} 个AB参数")
        print(f"   AB参数: {ab_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试优化后的数据提取架构")
    
    tests = [
        ("配置加载", test_config_loading),
        ("参数映射", test_parameter_mapping),
        ("数据提取", test_config_based_extractor),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！新架构工作正常。")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")


if __name__ == "__main__":
    main()
