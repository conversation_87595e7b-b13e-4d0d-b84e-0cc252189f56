#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译数据库模型定义
"""

from enum import Enum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

class TermType(Enum):
    """术语类型"""
    BASIC = "basic"                    # 基础术语
    TECHNICAL = "technical"            # 技术术语
    COMPOUND = "compound"              # 复合词汇
    SMART_PATTERN = "smart_pattern"    # 智能模式
    UNIT_PATTERN = "unit_pattern"      # 单位模式
    PRODUCT_DESC = "product_desc"      # 产品描述
    PROCESS_FLOW = "process_flow"      # 工艺流程

class TranslationSource(Enum):
    """翻译来源"""
    INITIAL = "initial"                # 初始化数据
    MANUAL = "manual"                  # 手动添加
    AI_LEARNED = "ai_learned"          # AI学习
    USER_FEEDBACK = "user_feedback"    # 用户反馈
    AUTO_IMPROVED = "auto_improved"    # 自动改进

@dataclass
class TranslationTerm:
    """翻译术语数据类"""
    id: Optional[int] = None
    chinese: str = ""
    english: str = ""
    term_type: TermType = TermType.BASIC
    source: TranslationSource = TranslationSource.INITIAL
    confidence: float = 1.0            # 置信度 0.0-1.0
    usage_count: int = 0               # 使用次数
    success_rate: float = 1.0          # 成功率
    context: str = ""                  # 使用语境
    notes: str = ""                    # 备注
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True             # 是否启用

@dataclass
class SmartPattern:
    """智能模式数据类"""
    id: Optional[int] = None
    pattern: str = ""                  # 正则表达式模式
    replacement: str = ""              # 替换模板
    description: str = ""              # 模式描述
    priority: int = 100                # 优先级（数字越小优先级越高）
    source: TranslationSource = TranslationSource.INITIAL
    usage_count: int = 0
    success_rate: float = 1.0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = True

@dataclass
class TranslationLog:
    """翻译日志数据类"""
    id: Optional[int] = None
    original_text: str = ""
    translated_text: str = ""
    translation_method: str = ""       # 翻译方法：dict/smart/ai
    term_id: Optional[int] = None      # 关联的术语ID
    pattern_id: Optional[int] = None   # 关联的模式ID
    quality_score: float = 0.0         # 质量评分
    user_rating: Optional[int] = None  # 用户评分 1-5
    feedback: str = ""                 # 用户反馈
    created_at: Optional[datetime] = None

@dataclass
class LearningRecord:
    """学习记录数据类"""
    id: Optional[int] = None
    original_text: str = ""
    suggested_translation: str = ""
    current_translation: str = ""
    improvement_type: str = ""         # 改进类型：accuracy/context/style
    confidence: float = 0.0
    status: str = "pending"            # pending/approved/rejected
    created_at: Optional[datetime] = None
    reviewed_at: Optional[datetime] = None
    reviewer: str = ""                 # 审核者
