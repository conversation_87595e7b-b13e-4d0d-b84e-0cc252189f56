#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汇总表配置管理器
管理列映射、参数映射和程序设置
"""

import json
import os
from typing import Dict, List, Any
from pathlib import Path
from ..utils import get_logger

logger = get_logger(__name__)


class SummaryTableConfig:
    """汇总表配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.logger = get_logger(__name__)
        
        # 默认配置
        self.default_config = {
            "column_mapping": {
                "A": "序号",
                "B": "产品型号",
                "C": "外观(A)",
                "D": "外观(B)",
                "E": "粘度(A)",
                "F": "粘度(B)",
                "G": "密度(A)",
                "H": "密度(B)",
                "I": "混合比例",
                "J": "混合粘度",
                "K": "固化后密度",
                "L": "开放时间",
                "M": "不流动时间",
                "N": "固化时间",
                "O": "流淌性",
                "P": "硬度",
                "Q": "拉伸强度",
                "R": "断裂伸长率",
                "S": "剪切强度",
                "T": "绝缘强度",
                "U": "体积电阻率",
                "V": "介电常数",
                "W": "介质损耗因数",
                "X": "导热系数",
                "Y": "线型膨胀系数",
                "Z": "耐老化",
                "AA": "阻燃",
                "AB": "吸水率",
                "AC": "应用温度",
                "AD": "耐候性",
                "AE": "性能特点",
                "AF": "保存期限(A)",
                "AG": "保存期限(B)",
                "AH": "双85测试",
                "AI": "收缩率",
                "AJ": "玻璃化转变温度"
            },
            "param_mapping": {
                "外观(A)": ["外观"],
                "外观(B)": ["外观"],
                "粘度(A)": ["粘度"],
                "粘度(B)": ["粘度"],
                "密度(A)": ["密度"],
                "密度(B)": ["密度"],
                "保存期限(A)": ["保存期限"],
                "保存期限(B)": ["保存期限"],
                "混合比例": ["混合比例", "比例"],
                "混合粘度": ["混合粘度"],
                "固化后密度": ["混合密度", "固化后密度"],
                "开放时间": ["开放时间", "可操作时间", "可使用时长"],
                "不流动时间": ["不流动时间", "凝胶时间"],
                "固化时间": ["固化时间", "固化条件", "固化工艺"],
                "流淌性": ["流淌性"],
                "硬度": ["硬度"],
                "拉伸强度": ["拉伸强度"],
                "断裂伸长率": ["断裂伸长率", "伸长率"],
                "剪切强度": ["剪切强度"],
                "绝缘强度": ["绝缘强度", "介电强度", "耐电压"],
                "体积电阻率": ["体积电阻率", "电阻率"],
                "介电常数": ["介电常数"],
                "介质损耗因数": ["介质损耗因数", "损耗因数"],
                "导热系数": ["导热系数"],
                "线型膨胀系数": ["线性膨胀系数", "膨胀系数", "线型膨胀系数"],
                "耐老化": ["耐老化"],
                "阻燃": ["阻燃", "阻燃等级"],
                "吸水率": ["吸水率", "吸湿性"],
                "应用温度": ["应用温度范围", "应用温度", "使用温度", "耐温范围"],
                "耐候性": ["耐候性"],
                "性能特点": ["产品陈述", "性能特点", "产品描述", "产品概述"],
                "双85测试": ["双85测试", "85℃85%RH测试", "高温高湿测试"],
                "收缩率": ["收缩率", "体积收缩率"],
                "玻璃化转变温度": ["玻璃化转变温度", "Tg", "玻璃转化温度", "DSC"]
            },
            "settings": {
                "input_folder": "PDF",
                "output_file": "智能汇总表.xlsx",
                "data_start_row": 8,
                "header_row": 4,
                "ab_indicator_row": 5
            }
        }
        
        # 加载配置
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 尝试从多个位置加载配置文件
            config_paths = [
                self.config_file,
                f"dist/{self.config_file}",
                f"config/{self.config_file}"
            ]
            
            for config_path in config_paths:
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.logger.info(f"成功加载配置文件: {config_path}")
                    
                    # 合并默认配置和用户配置
                    merged_config = self.default_config.copy()
                    merged_config.update(config)
                    
                    return merged_config
            
            # 如果没有找到配置文件，使用默认配置
            self.logger.warning(f"未找到配置文件 {self.config_file}，使用默认配置")
            return self.default_config.copy()
            
        except Exception as e:
            self.logger.error(f"配置文件加载失败: {e}")
            self.logger.info("使用默认配置")
            return self.default_config.copy()
    
    def get_column_mapping(self) -> Dict[str, str]:
        """获取列映射配置"""
        return self.config.get("column_mapping", {})
    
    def get_param_mapping(self) -> Dict[str, List[str]]:
        """获取参数映射配置"""
        return self.config.get("param_mapping", {})
    
    def get_settings(self) -> Dict[str, Any]:
        """获取程序设置"""
        return self.config.get("settings", {})
    
    def get_input_folder(self) -> str:
        """获取输入文件夹路径"""
        return self.get_settings().get("input_folder", "PDF")
    
    def get_output_file(self) -> str:
        """获取输出文件名"""
        return self.get_settings().get("output_file", "智能汇总表.xlsx")
    
    def get_data_start_row(self) -> int:
        """获取数据开始行号"""
        return self.get_settings().get("data_start_row", 8)
    
    def get_header_row(self) -> int:
        """获取表头行号"""
        return self.get_settings().get("header_row", 4)
    
    def get_ab_indicator_row(self) -> int:
        """获取A/B标识行号"""
        return self.get_settings().get("ab_indicator_row", 5)
    
    def save_config(self, config_path: str = None):
        """保存配置到文件"""
        try:
            save_path = config_path or self.config_file
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置文件已保存: {save_path}")
            
        except Exception as e:
            self.logger.error(f"配置文件保存失败: {e}")
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        self.logger.info("配置已更新")
    
    def get_ab_parameters(self) -> List[str]:
        """获取需要A/B组分处理的参数列表"""
        ab_params = []
        column_mapping = self.get_column_mapping()
        
        for param_name in column_mapping.values():
            if "(A)" in param_name or "(B)" in param_name:
                base_param = param_name.replace("(A)", "").replace("(B)", "")
                if base_param not in ab_params:
                    ab_params.append(base_param)
        
        return ab_params
    
    def is_ab_parameter(self, param_name: str) -> bool:
        """判断参数是否需要A/B组分处理"""
        ab_params = self.get_ab_parameters()
        return any(ab_param in param_name for ab_param in ab_params)
    
    def get_column_order(self) -> List[str]:
        """获取列的顺序"""
        column_mapping = self.get_column_mapping()
        
        # 按Excel列顺序排序
        def column_sort_key(col):
            if len(col) == 1:
                return (1, ord(col))
            else:
                return (2, ord(col[0]), ord(col[1]))
        
        return sorted(column_mapping.keys(), key=column_sort_key)
