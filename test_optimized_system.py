#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger
from src.summary_table import ConfigBasedExtractor, SummaryTableGenerator, SummaryTableConfig

logger = get_logger(__name__)


def test_optimized_system():
    """测试优化后的系统"""
    print("🧪 测试优化后的智能汇总表生成器 v3.0")
    print("=" * 60)
    
    try:
        # 初始化组件
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        generator = SummaryTableGenerator(config)
        
        # 扫描文件
        test_folder = "PDF"
        if not os.path.exists(test_folder):
            print(f"❌ 测试文件夹不存在: {test_folder}")
            return False
        
        files = extractor.scan_folder(test_folder)
        print(f"📁 找到 {len(files)} 个支持的文件")
        
        if not files:
            print("⚠️ 没有找到测试文件")
            return True
        
        # 提取数据
        all_data = []
        for i, file_path in enumerate(files[:3]):  # 只测试前3个文件
            filename = os.path.basename(file_path)
            print(f"\n📄 处理文件 {i+1}/3: {filename}")
            
            result = extractor.extract_from_file(file_path)
            if result:
                all_data.append(result)
                print(f"   ✅ 成功提取 {len(result['parameters'])} 个参数")
                
                # 显示AB参数
                ab_params = {k: v for k, v in result['parameters'].items() 
                           if k.endswith('(A)') or k.endswith('(B)')}
                if ab_params:
                    print(f"   🔬 AB组分参数: {len(ab_params)} 个")
            else:
                print(f"   ❌ 提取失败")
        
        if not all_data:
            print("\n❌ 没有成功提取到任何数据")
            return False
        
        # 生成Excel
        output_file = "测试汇总表_v3.xlsx"
        print(f"\n📊 生成Excel汇总表: {output_file}")
        
        success = generator.generate_summary_table(all_data, output_file)
        
        if success:
            print("✅ Excel汇总表生成成功！")
            print(f"📁 输出文件: {output_file}")
            
            # 显示统计信息
            total_params = sum(len(data['parameters']) for data in all_data)
            ab_params = sum(1 for data in all_data for param in data['parameters'] 
                          if param.endswith('(A)') or param.endswith('(B)'))
            
            print(f"\n📈 统计信息:")
            print(f"   处理文件: {len(all_data)} 个")
            print(f"   总参数数: {total_params} 个")
            print(f"   AB参数数: {ab_params} 个")
            
            return True
        else:
            print("❌ Excel汇总表生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    success = test_optimized_system()
    
    if success:
        print("\n🎉 系统测试通过！优化后的架构工作正常。")
    else:
        print("\n⚠️ 系统测试失败，需要进一步调试。")


if __name__ == "__main__":
    main()
