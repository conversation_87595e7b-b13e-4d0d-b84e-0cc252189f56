# 配置文件说明

## 概述

`config.json` 文件包含了智能汇总表生成器的所有配置信息，用户可以通过修改此文件来自定义字段映射和程序行为，无需修改源代码。

## 配置文件结构

### 1. column_mapping（列映射）

定义Excel汇总表中每列对应的参数名称。

```json
"column_mapping": {
    "A": "序号",
    "B": "产品型号",
    "C": "外观(A)",
    "D": "外观(B)",
    ...
}
```

**说明：**
- 键：Excel列标识（A, B, C, ..., AA, AB, AC, AD, AE）
- 值：该列显示的参数名称

**可自定义：**
- 修改列标题名称
- 调整列的用途
- 添加或删除列（需要同时修改param_mapping）

### 2. param_mapping（参数映射）

定义如何从原始文档中匹配参数到汇总表列。

```json
"param_mapping": {
    "外观(A)": ["外观"],
    "粘度(A)": ["粘度"],
    "混合比例": ["混合比例", "比例"],
    "开放时间": ["开放时间", "可操作时间", "可使用时长"],
    ...
}
```

**说明：**
- 键：汇总表中的参数名称（对应column_mapping中的值）
- 值：原始文档中可能出现的参数名称列表（支持多个别名）

**自定义示例：**
```json
"硬度": ["硬度", "Shore硬度", "邵氏硬度", "硬度值"]
```

### 3. settings（程序设置）

控制程序的基本行为参数。

```json
"settings": {
    "input_folder": "PDF",
    "output_file": "智能汇总表.xlsx",
    "data_start_row": 8,
    "header_row": 4,
    "ab_indicator_row": 5
}
```

**参数说明：**
- `input_folder`: 输入文件夹路径
- `output_file`: 输出Excel文件名
- `data_start_row`: 数据开始填写的行号
- `header_row`: 表头行号
- `ab_indicator_row`: A/B组分标识行号

## 自定义指南

### 添加新的参数映射

1. 在 `column_mapping` 中添加新列：
```json
"AF": "新参数名称"
```

2. 在 `param_mapping` 中添加对应的匹配规则：
```json
"新参数名称": ["原始文档中的参数名1", "参数名2", "参数名3"]
```

### 修改现有参数的匹配规则

例如，为"硬度"参数添加更多匹配关键词：

```json
"硬度": ["硬度", "Shore硬度", "邵氏硬度", "硬度值", "Shore A", "Shore D"]
```

### 修改文件路径和名称

```json
"settings": {
    "input_folder": "技术文档",
    "output_file": "产品参数汇总表.xlsx"
}
```

### 调整表格布局

```json
"settings": {
    "data_start_row": 10,    // 从第10行开始填写数据
    "header_row": 6,         // 第6行为表头
    "ab_indicator_row": 7    // 第7行为A/B标识
}
```

## 常见自定义场景

### 1. 添加新的技术参数

如果需要添加"玻璃化转变温度"参数：

1. 在 `column_mapping` 中添加：
```json
"AF": "玻璃化转变温度"
```

2. 在 `param_mapping` 中添加：
```json
"玻璃化转变温度": ["玻璃化转变温度", "Tg", "玻璃转化温度", "玻璃态转变温度"]
```

### 2. 修改A/B组分参数

如果原始文档中A/B组分的标识不同，可以修改匹配规则：

```json
"外观(A)": ["外观", "A组分外观", "A剂外观"],
"外观(B)": ["外观", "B组分外观", "B剂外观"]
```

### 3. 支持不同语言

添加英文参数名支持：

```json
"密度(A)": ["密度", "Density", "比重", "密度值"],
"粘度(A)": ["粘度", "Viscosity", "黏度", "粘度值"]
```

## 注意事项

1. **JSON格式**：确保配置文件符合JSON格式规范，注意逗号和引号
2. **编码格式**：配置文件应保存为UTF-8编码
3. **备份配置**：修改前建议备份原始配置文件
4. **测试验证**：修改后运行程序验证配置是否正确

## 错误处理

如果配置文件格式错误或不存在，程序会：
1. 显示警告信息
2. 自动使用内置的默认配置
3. 继续正常运行

## 配置文件位置

配置文件 `config.json` 应与 `智能汇总表生成器.py` 放在同一目录下。
