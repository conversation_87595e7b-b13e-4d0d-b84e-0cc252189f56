#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
提供分级日志记录、彩色输出、文件记录等功能
"""

import logging
import os
from datetime import datetime
from pathlib import Path
import colorlog

class SmartLogger:
    """智能日志记录器"""
    
    def __init__(self, name="SmartTableGenerator", log_dir="logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 创建日志器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器（彩色输出）
        console_handler = colorlog.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
        
        # 文件处理器（详细日志）
        log_file = self.log_dir / f"{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        
        # 错误文件处理器
        error_file = self.log_dir / f"error_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        
        # 添加处理器
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
    
    def debug(self, message, **kwargs):
        """调试信息"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message, **kwargs):
        """一般信息"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message, **kwargs):
        """警告信息"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message, **kwargs):
        """错误信息"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message, **kwargs):
        """严重错误"""
        self.logger.critical(message, **kwargs)
    
    def log_file_processing(self, filename, status, details=None):
        """记录文件处理状态"""
        if status == "start":
            self.info(f"开始处理文件: {filename}")
        elif status == "success":
            self.info(f"✅ 成功处理文件: {filename}")
            if details:
                self.debug(f"处理详情: {details}")
        elif status == "error":
            self.error(f"❌ 处理文件失败: {filename}")
            if details:
                self.error(f"错误详情: {details}")
        elif status == "warning":
            self.warning(f"⚠️  处理文件有警告: {filename}")
            if details:
                self.warning(f"警告详情: {details}")
    
    def log_data_quality(self, filename, missing_params, total_params):
        """记录数据质量信息"""
        if missing_params:
            completion_rate = (total_params - len(missing_params)) / total_params * 100
            self.warning(f"数据完整性: {filename} - {completion_rate:.1f}% ({len(missing_params)}个参数缺失)")
            self.debug(f"缺失参数: {missing_params}")
        else:
            self.info(f"数据完整性: {filename} - 100% (所有参数完整)")
    
    def log_performance(self, operation, duration, file_count=None):
        """记录性能信息"""
        if file_count:
            avg_time = duration / file_count
            self.info(f"性能统计: {operation} - 总耗时{duration:.2f}秒, 平均{avg_time:.2f}秒/文件")
        else:
            self.info(f"性能统计: {operation} - 耗时{duration:.2f}秒")

# 全局日志器实例
logger = SmartLogger()

# 便捷函数
def get_logger(name=None):
    """获取日志器实例"""
    if name:
        return SmartLogger(name)
    return logger

def log_exception(func):
    """装饰器：自动记录异常"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行异常: {str(e)}")
            raise
    return wrapper
