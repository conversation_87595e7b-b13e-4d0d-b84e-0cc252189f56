#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术词典系统
专门为TDS技术参数表设计的专业术语翻译词典
"""

import re
from typing import Dict, List, Tuple
from ..utils import get_logger
from ..database import TranslationDatabase
from ..database.migration import DataMigration

logger = get_logger(__name__)

class TechnicalDictionary:
    """技术词典 - 专业术语翻译"""
    
    def __init__(self, use_database: bool = True):
        """初始化技术词典"""
        self.logger = get_logger(__name__)
        self.use_database = use_database

        if use_database:
            self.db = TranslationDatabase()
            self._ensure_data_migrated()
            self.dictionary = self.db.get_all_terms()
            self.smart_patterns = self.db.get_smart_patterns()
        else:
            # 回退到文件模式
            self._load_from_files()

        self.unit_patterns = self._build_unit_patterns()
        self.standard_patterns = self._build_standard_patterns()

        self.logger.info(f"技术词典加载完成，包含 {len(self.dictionary)} 个术语，{len(self.smart_patterns)} 个智能模式")
    
    def _ensure_data_migrated(self):
        """确保数据已迁移到数据库"""
        stats = self.db.get_statistics()
        if stats['total_terms'] == 0:
            self.logger.info("检测到空数据库，开始迁移数据...")
            migration = DataMigration(self.db)
            if migration.migrate_from_dictionary_data():
                self.logger.info("数据迁移完成")
                migration.validate_migration()
            else:
                self.logger.error("数据迁移失败，回退到文件模式")
                self.use_database = False
                self._load_from_files()

    def _load_from_files(self):
        """从文件加载数据（回退模式）"""
        try:
            from .dictionary_data import TECHNICAL_DICTIONARY
            from .smart_patterns import SMART_PATTERNS
            self.dictionary = TECHNICAL_DICTIONARY
            self.smart_patterns = SMART_PATTERNS
            self.logger.info("使用文件模式加载数据")
        except ImportError as e:
            self.logger.error(f"无法加载文件数据: {e}")
            self.dictionary = {}
            self.smart_patterns = []

    def _build_dictionary(self) -> Dict[str, str]:
        """构建完整的技术词典（兼容性方法）"""
        if hasattr(self, 'dictionary'):
            return self.dictionary
        return {}

    def _build_unit_patterns(self) -> List[Tuple[str, str]]:
        """构建单位转换模式"""
        try:
            from .dictionary_data import UNIT_PATTERNS
            return UNIT_PATTERNS
        except ImportError:
            return [
                (r'(\d+)℃', r'\1°C'),
                (r'(\d+±\d+)℃', r'\1°C'),
                (r'(\d+~\d+)℃', r'\1°C'),
            ]

    def _build_standard_patterns(self) -> List[Tuple[str, str]]:
        """构建标准转换模式"""
        try:
            from .dictionary_data import STANDARD_PATTERNS
            return STANDARD_PATTERNS
        except ImportError:
            return [
                (r'GB/T\s*(\d+)', r'GB/T \1'),
                (r'企业标准', 'Enterprise Standard'),
            ]

    def _build_smart_patterns(self) -> List[Tuple[str, str]]:
        """构建智能模式匹配（兼容性方法）"""
        if hasattr(self, 'smart_patterns'):
            return self.smart_patterns
        return []

    
    def translate_term(self, term: str) -> str:
        """翻译单个术语"""
        # 标准化文本
        original_term = term
        term = self._normalize_text(term)

        # 1. 检查是否不包含中文字符，但需要符号转换
        if not self._contains_chinese(term):
            if self._needs_symbol_conversion(original_term):
                return self._convert_symbols(original_term)
            return original_term

        # 2. 检查是否为不需要翻译的内容
        if self._is_non_translatable_content(term):
            return original_term

        # 3. 检查是否不包含有意义的中文内容，但可能需要符号转换
        if not self._contains_meaningful_chinese(term):
            if self._needs_symbol_conversion(original_term):
                return self._convert_symbols(original_term)
            return original_term

        # 4. 直接查找词典
        if term in self.dictionary:
            return self.dictionary[term]

        # 5. 尝试查找标准化前的原文
        if original_term in self.dictionary:
            return self.dictionary[original_term]

        # 6. 优先检查是否为工艺流程（在智能模式之前）
        if self._is_process_flow(term):
            result = self._translate_process_flow_advanced(term)
            # 质量检查：如果翻译质量不佳，标记为需要AI翻译
            if not self._is_translation_quality_good(original_term, result):
                return None  # 返回None表示需要AI翻译
            return result

        # 7. 智能模式匹配
        for pattern, replacement in self.smart_patterns:
            if re.search(pattern, term):
                result = re.sub(pattern, replacement, term)
                # 转换温度单位和其他单位
                result = re.sub(r'(\d+)℃', r'\1°C', result)
                result = re.sub(r'铁桶', 'Iron Drum', result)
                result = re.sub(r'塑料桶', 'Plastic Drum', result)
                result = re.sub(r'(\d+)个月', r'\1 months', result)
                result = re.sub(r'(\d+)分钟', r'\1 minutes', result)
                result = re.sub(r'(\d+)次', r'\1 times', result)
                result = re.sub(r'(\d+)小时', r'\1 hours', result)

                # 质量检查：如果智能模式翻译质量不佳，继续尝试其他方法
                if not self._is_translation_quality_good(original_term, result):
                    continue  # 尝试下一个模式或方法
                return result

        # 8. 智能表格内容分割翻译
        table_result = self._translate_table_content(term)
        if table_result != term:
            # 质量检查：如果表格翻译质量不佳，返回None让AI处理
            if not self._is_translation_quality_good(original_term, table_result):
                return None  # 返回None表示需要AI翻译
            return table_result

        # 8. 处理单位
        for pattern, replacement in self.unit_patterns:
            if re.search(pattern, term):
                return re.sub(pattern, replacement, term)

        # 9. 处理标准
        for pattern, replacement in self.standard_patterns:
            if re.search(pattern, term):
                return re.sub(pattern, replacement, term)

        # 10. 处理数值范围和比例（保持不变）
        if re.match(r'^[\d\.\±\~\>\<\≤\≥\-\+\|\s\:]+$', term):
            return term

        # 未找到翻译，返回原文
        return term

    def _translate_table_content(self, text: str) -> str:
        """智能翻译表格内容和多步骤流程"""
        # 检测是否为多步骤工艺流程
        if self._is_process_flow(text):
            return self._translate_process_flow(text)

        # 检测是否为表格行（包含多个用空格分隔的单元格）
        if not self._is_table_row(text):
            return text

        # 分割表格单元格
        cells = self._split_table_cells(text)

        # 翻译每个单元格
        translated_cells = []
        for cell in cells:
            cell = cell.strip()
            if not cell:
                continue

            # 直接查找词典
            if cell in self.dictionary:
                translated_cells.append(self.dictionary[cell])
            # 处理单位
            elif self._translate_with_patterns(cell, self.unit_patterns):
                translated_cells.append(self._translate_with_patterns(cell, self.unit_patterns))
            # 处理标准
            elif self._translate_with_patterns(cell, self.standard_patterns):
                translated_cells.append(self._translate_with_patterns(cell, self.standard_patterns))
            # 数值保持不变
            elif re.match(r'^[\d\.\±\~\>\<\≤\≥\-\+\|\(\)]+$', cell):
                translated_cells.append(cell)
            else:
                translated_cells.append(cell)

        return ' '.join(translated_cells)

    def _is_table_row(self, text: str) -> bool:
        """判断是否为表格行"""
        # 简单启发式：包含多个用空格分隔的词，且包含技术术语
        words = text.split()
        if len(words) < 3:
            return False

        # 检查是否包含技术术语特征
        has_tech_terms = any(word in self.dictionary for word in words)
        has_units = any(re.search(pattern, text) for pattern, _ in self.unit_patterns)
        has_standards = any(re.search(pattern, text) for pattern, _ in self.standard_patterns)
        has_numbers = bool(re.search(r'\d', text))

        return has_tech_terms or has_units or has_standards or has_numbers

    def _split_table_cells(self, text: str) -> list:
        """智能分割表格单元格"""
        # 使用多个空格作为分隔符
        cells = re.split(r'\s{2,}', text)
        if len(cells) == 1:
            # 如果没有多个空格，尝试其他分割方式
            cells = re.split(r'\s+', text)

        return [cell.strip() for cell in cells if cell.strip()]

    def _is_process_flow(self, text: str) -> bool:
        """检测是否为多步骤工艺流程"""
        # 检查是否包含工艺流程的特征
        process_indicators = [
            r'手工灌胶工艺：',
            r'机器灌胶工艺：',
            r'\d+\.\s*(预热|混合|搅拌|脱泡|浇注|浇筑|装料|程序设定|空放|固化)：',
            r'\d+\.\s*(储存条件|储存期限|使用环境)：'
        ]

        for pattern in process_indicators:
            if re.search(pattern, text):
                return True

        # 检查是否包含多个编号步骤
        numbered_steps = re.findall(r'\d+\.\s*[^；。\n]*[；。]', text)
        if len(numbered_steps) >= 2:
            return True

        # 检查是否包含多行内容且有工艺相关关键词
        lines = text.split('\n')
        if len(lines) > 3:  # 多行内容
            process_keywords = ['预热', '混合', '搅拌', '脱泡', '浇注', '浇筑', '装料', '程序设定', '空放', '固化', '储存条件', '储存期限', '使用环境']
            keyword_count = sum(1 for keyword in process_keywords if keyword in text)
            if keyword_count >= 3:  # 包含3个或以上工艺关键词
                return True

        return False

    def _translate_process_flow(self, text: str) -> str:
        """翻译多步骤工艺流程"""
        # 首先按行分割，处理标题
        lines = text.split('\n')
        translated_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                translated_lines.append('')
                continue

            # 按照句子分割（以；或。结尾）
            sentences = re.split(r'([；。])', line)
            translated_parts = []

            for i in range(0, len(sentences), 2):
                if i < len(sentences):
                    sentence = sentences[i].strip()
                    separator = sentences[i + 1] if i + 1 < len(sentences) else ''

                    if sentence:
                        # 翻译单个句子
                        translated_sentence = self._translate_single_segment(sentence)
                        translated_parts.append(translated_sentence + separator)
                    elif separator:
                        translated_parts.append(separator)

            translated_lines.append(''.join(translated_parts))

        return '\n'.join(translated_lines)

    def _translate_process_flow_advanced(self, text: str) -> str:
        """高级工艺流程翻译方法（稳健版本）"""
        result = text
        original_length = len(text)

        # 安全检查：如果文本过短或过长，使用标准翻译
        if original_length < 50 or original_length > 5000:
            return self._translate_single_segment(result)

        # 1. 保存原始格式信息
        original_lines = text.split('\n')

        # 2. 逐行处理，保持格式
        translated_lines = []
        for line in original_lines:
            if not line.strip():
                translated_lines.append(line)  # 保持空行
                continue

            translated_line = self._translate_line_safely(line.strip())
            # 保持原始行的缩进
            indent = len(line) - len(line.lstrip())
            translated_lines.append(' ' * indent + translated_line)

        result = '\n'.join(translated_lines)

        # 3. 最终安全检查
        if self._is_translation_valid(text, result):
            return result
        else:
            # 如果翻译结果不合理，回退到标准方法
            return self._translate_single_segment(text)

    def _translate_line_safely(self, line: str) -> str:
        """安全地翻译单行文本"""
        result = line

        # 1. 应用智能模式（限制迭代次数）
        for _ in range(2):  # 最多迭代2次
            old_result = result
            for pattern, replacement in self.smart_patterns:
                if re.search(pattern, result):
                    try:
                        new_result = re.sub(pattern, replacement, result)
                        # 安全检查：确保替换合理
                        if len(new_result) < len(result) * 3:  # 避免异常膨胀
                            result = new_result
                    except Exception:
                        continue  # 如果正则替换失败，跳过
            if result == old_result:
                break

        # 2. 应用单位转换
        result = self._apply_unit_conversions(result)

        # 3. 应用复合词汇处理
        result = self._handle_compound_words(result)

        # 4. 安全的词汇翻译
        result = self._translate_word_by_word_safely(result)

        return result

    def _is_translation_valid(self, original: str, translated: str) -> bool:
        """检查翻译结果是否合理"""
        # 基本合理性检查
        if not translated or len(translated) > len(original) * 5:
            return False

        # 检查是否还有大量未翻译的中文
        remaining_chinese = re.findall(r'[\u4e00-\u9fa5]+', translated)
        chinese_ratio = len(''.join(remaining_chinese)) / len(original) if original else 0

        # 如果超过50%的中文未翻译，认为翻译失败
        return chinese_ratio < 0.5

    def _is_translation_quality_good(self, original: str, translated: str) -> bool:
        """检查翻译质量是否足够好，避免需要AI翻译"""
        if not translated or translated == original:
            return False

        # 检查剩余中文比例
        remaining_chinese = re.findall(r'[\u4e00-\u9fa5]+', translated)
        original_chinese = re.findall(r'[\u4e00-\u9fa5]+', original)

        if not original_chinese:  # 原文没有中文
            return True

        # 计算翻译完成度
        remaining_ratio = len(''.join(remaining_chinese)) / len(''.join(original_chinese))

        # 如果超过20%的中文未翻译，认为质量不佳
        if remaining_ratio > 0.2:
            return False

        # 检查是否有英中混杂的问题
        if self._has_mixed_language_issues(translated):
            return False

        return True

    def _has_mixed_language_issues(self, text: str) -> bool:
        """检查是否有英中混杂的问题"""
        # 检查是否有中英文混杂的不自然表达
        mixed_patterns = [
            r'[a-zA-Z]+[\u4e00-\u9fa5]+[a-zA-Z]+',  # 英中英混杂
            r'[\u4e00-\u9fa5]+[a-zA-Z]+[\u4e00-\u9fa5]+',  # 中英中混杂
            r'[a-zA-Z]+：[\u4e00-\u9fa5]+',  # 英文标题+中文内容
            r'[\u4e00-\u9fa5]+：[a-zA-Z]+',  # 中文标题+英文内容（部分翻译）
        ]

        for pattern in mixed_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _translate_word_by_word(self, text: str) -> str:
        """逐词翻译剩余的中文内容"""
        result = text

        # 获取所有中文词汇，按长度降序排列
        chinese_words = re.findall(r'[\u4e00-\u9fa5]+', result)
        chinese_words = sorted(set(chinese_words), key=len, reverse=True)

        # 逐个翻译
        for word in chinese_words:
            if word in self.dictionary:
                # 确保精确替换，避免部分匹配
                result = result.replace(word, self.dictionary[word])

        return result

    def _translate_word_by_word_safely(self, text: str) -> str:
        """安全地逐词翻译剩余的中文内容"""
        result = text

        # 获取所有中文词汇，按长度降序排列
        chinese_words = re.findall(r'[\u4e00-\u9fa5]+', result)
        chinese_words = sorted(set(chinese_words), key=len, reverse=True)

        # 限制翻译的词汇数量，避免过度处理
        max_words = min(50, len(chinese_words))

        # 逐个翻译（带安全检查）
        for word in chinese_words[:max_words]:
            if word in self.dictionary and len(word) >= 2:  # 只翻译长度>=2的词汇
                try:
                    # 确保精确替换，避免部分匹配
                    old_result = result
                    result = result.replace(word, self.dictionary[word])

                    # 安全检查：如果替换后长度异常，回退
                    if len(result) > len(old_result) * 2:
                        result = old_result

                except Exception:
                    continue  # 如果替换失败，跳过

        return result

    def _translate_single_segment(self, segment: str) -> str:
        """翻译单个文本段落"""
        result = segment

        # 1. 应用所有匹配的智能模式
        for pattern, replacement in self.smart_patterns:
            if re.search(pattern, result):
                result = re.sub(pattern, replacement, result)

        # 2. 应用单位转换
        result = self._apply_unit_conversions(result)

        # 3. 翻译剩余的中文词汇
        result = self._translate_remaining_chinese(result)

        return result

    def _translate_single_sentence(self, sentence: str) -> str:
        """翻译单个句子"""
        # 检查智能模式匹配
        for pattern, replacement in self.smart_patterns:
            if re.search(pattern, sentence):
                result = re.sub(pattern, replacement, sentence)
                # 应用单位转换
                result = self._apply_unit_conversions(result)
                return result

        # 检查词典匹配
        if sentence in self.dictionary:
            return self.dictionary[sentence]

        # 如果没有匹配，返回原句
        return sentence

    def _apply_unit_conversions(self, text: str) -> str:
        """应用单位转换"""
        result = text
        result = re.sub(r'(\d+)℃', r'\1°C', result)
        result = re.sub(r'(\d+)个月', r'\1 months', result)
        result = re.sub(r'(\d+)分钟', r'\1 minutes', result)
        result = re.sub(r'(\d+)次', r'\1 times', result)
        result = re.sub(r'(\d+)小时', r'\1 hours', result)
        return result

    def _translate_remaining_chinese(self, text: str) -> str:
        """翻译剩余的中文词汇"""
        result = text

        # 首先处理复合词汇和短语
        result = self._handle_compound_words(result)

        # 然后查找所有中文词汇并尝试翻译（按长度降序排列，优先翻译长词）
        chinese_words = re.findall(r'[\u4e00-\u9fa5]+', result)
        chinese_words = sorted(set(chinese_words), key=len, reverse=True)

        for word in chinese_words:
            if word in self.dictionary:
                # 使用更精确的替换，避免部分匹配
                # 只替换完整的词汇，不替换词汇的一部分
                if word in result:
                    result = result.replace(word, self.dictionary[word])

        return result

    def _handle_compound_words(self, text: str) -> str:
        """处理复合词汇和常见短语"""
        result = text

        # 处理常见的复合表达（按长度降序排列，优先处理长短语）
        compound_patterns = [
            # 长短语优先
            (r'本品对湿气敏感，潮气会造成固化气泡', r'This product is sensitive to moisture, which may cause curing bubbles'),
            (r'温度低应酌情延长固化时间', r'Extend curing time appropriately at low temperatures'),
            (r'粘度会变高', r'viscosity increases'),
            (r'易结晶', r'tends to crystallize'),
            (r'请预热材料至', r'Please preheat materials to'),
            (r'便于使用', r'for easy use'),
            (r'可固化', r'can be cured'),
            (r'潮气会造成', r'moisture may cause'),
            (r'本品对湿气敏感', r'This product is sensitive to moisture'),

            # 组份相关
            (r'([AB])组份粘度会变高', r'Component \1 viscosity increases'),
            (r'([AB])组份易结晶', r'Component \1 tends to crystallize'),
            (r'([AB])组份', r'Component \1'),
            (r'A、B组份', r'A and B components'),
            (r'A/B组份', r'A/B components'),

            # 数量单位
            (r'(\d+)个月', r'\1 months'),
            (r'(\d+)分钟', r'\1 minutes'),
            (r'(\d+)次', r'\1 times'),
            (r'(\d+)小时', r'\1 hours'),

            # 温度单位
            (r'(\d+~\d+)℃', r'\1°C'),
            (r'(\d+±\d+)℃', r'\1°C'),
            (r'(\d+)℃', r'\1°C'),

            # 常见连接词
            (r'\s*或\s*', r' or '),
            (r'请', r'Please'),
            (r'材料至', r'materials to'),
            (r'可', r'can'),
            (r'气泡', r'bubbles'),
        ]

        for pattern, replacement in compound_patterns:
            result = re.sub(pattern, replacement, result)

        return result

    def _translate_with_patterns(self, text: str, patterns: list) -> str:
        """使用模式列表翻译文本"""
        for pattern, replacement in patterns:
            if re.search(pattern, text):
                return re.sub(pattern, replacement, text)
        return text
    
    def is_technical_term(self, text: str) -> bool:
        """判断是否为技术术语或不需要翻译的内容"""
        text = text.strip()

        # 1. 检查是否不包含中文字符（不需要翻译）
        if not self._contains_chinese(text):
            return True

        # 2. 检查是否为纯数值、符号或特殊格式（不需要翻译）
        if self._is_non_translatable_content(text):
            return True

        # 3. 检查是否不包含有意义的中文内容（不需要翻译）
        if not self._contains_meaningful_chinese(text):
            return True

        # 4. 检查是否在词典中
        if text in self.dictionary:
            return True

        # 5. 检查智能模式匹配
        for pattern, _ in self.smart_patterns:
            if re.search(pattern, text):
                return True

        # 6. 检查是否为表格内容
        if self._is_table_row(text):
            return True

        # 7. 检查是否为单位
        for pattern, _ in self.unit_patterns:
            if re.search(pattern, text):
                return True

        # 8. 检查是否为标准
        for pattern, _ in self.standard_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _is_non_translatable_content(self, text: str) -> bool:
        """检查是否为不需要翻译的内容"""
        # 纯数值、符号、比例等（包含中文符号）
        numeric_patterns = [
            r'^[\d\.\±\~\>\<\≤\≥\-\+\|\s\:()（）＜＞]+$',  # 纯数字和符号（包含中文符号）
            r'^[\d\.\±\~\>\<\≤\≥\-\+\|\s\:A-Z()（）＜＞]+$',  # 数字+字母（产品型号等）
            r'^\d+\s*/\s*\d+$',  # 分数格式
            r'^\d{4}-\d{2}/[A-Z]-\d+$',  # 日期版本格式
            r'^[A-Z]{2,}-\d+[A-Z]*(/[A-Z])?$',  # 产品型号格式
            r'^\d{3}-\d{2}[A-Z](/[A-Z])?$',  # 简化产品型号
            r'^www\.[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$',  # 网址
            r'^\d{3,4}-\d{8,}$',  # 电话号码
            r'^[＜<]\s*\d+(\.\d+)?$',  # 小于符号+数字
            r'^[＞>]\s*\d+(\.\d+)?$',  # 大于符号+数字
            r'^\d+~\d+\(\d+g\)$',  # 范围+重量格式
            r'^FM-[A-Z0-9\-]+$',  # FM产品型号
            r'^\d+:\d+$',  # 比例格式如100:100
        ]

        for pattern in numeric_patterns:
            if re.match(pattern, text):
                return True

        # 这些复杂格式应该通过智能模式处理，而不是标记为不可翻译
        # 移除过于宽泛的特殊格式检测

        return False

    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文字符（包括中文符号）"""
        import re
        # 检查中文汉字
        chinese_chars = re.findall(r'[\u4e00-\u9fa5]', text)  # 基本汉字范围
        # 检查中文符号（包括℃等）
        chinese_symbols = re.findall(r'[℃＜＞，：。；、（）]', text)
        return len(chinese_chars) > 0 or len(chinese_symbols) > 0

    def _contains_meaningful_chinese(self, text: str) -> bool:
        """检查文本是否包含有意义的中文内容（需要翻译）"""
        import re
        # 移除所有数字、符号、英文字母
        cleaned_text = re.sub(r'[0-9a-zA-Z\s\.\±\~\>\<\≤\≥\-\+\|\:()（）＜＞/：，。；、]', '', text)
        # 检查剩余的中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fa5]', cleaned_text)
        return len(chinese_chars) >= 2  # 至少2个中文字符才认为需要翻译

    def _needs_symbol_conversion(self, text: str) -> bool:
        """检查是否需要符号转换（如中文符号转英文符号）"""
        import re
        # 检查是否包含需要转换的中文符号（包括℃）
        chinese_symbols = re.findall(r'[℃＜＞，：。；、（）]', text)
        return len(chinese_symbols) > 0

    def _convert_symbols(self, text: str) -> str:
        """转换中文符号为英文符号"""
        import re
        # 符号转换映射
        symbol_map = {
            '℃': '°C',
            '＜': '<',
            '＞': '>',
            '，': ',',
            '：': ':',
            '。': '.',
            '；': ';',
            '、': ',',
            '（': '(',
            '）': ')'
        }

        result = text
        for chinese_symbol, english_symbol in symbol_map.items():
            result = result.replace(chinese_symbol, english_symbol)

        return result

    def _normalize_text(self, text: str) -> str:
        """标准化文本，处理空格和标点符号"""
        import re
        # 去除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        # 标准化中文标点符号周围的空格
        text = re.sub(r'\s*([，。；：、])\s*', r'\1', text)
        text = re.sub(r'\s*([（）])\s*', r'\1', text)
        return text

    def get_translation_coverage(self, texts: List[str]) -> Dict[str, any]:
        """获取翻译覆盖率统计"""
        total = len(texts)
        covered = 0
        uncovered_terms = []
        
        for text in texts:
            if self.is_technical_term(text):
                covered += 1
            else:
                uncovered_terms.append(text)
        
        return {
            'total': total,
            'covered': covered,
            'coverage_rate': covered / total if total > 0 else 0,
            'uncovered_terms': uncovered_terms
        }

# 全局技术词典实例
_technical_dictionary = None

def get_technical_dictionary() -> TechnicalDictionary:
    """获取技术词典实例"""
    global _technical_dictionary
    if _technical_dictionary is None:
        _technical_dictionary = TechnicalDictionary()
    return _technical_dictionary
