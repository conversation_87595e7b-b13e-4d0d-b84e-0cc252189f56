#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档翻译器
保持原始格式的Word文档翻译
"""

import os
from pathlib import Path
from typing import List, Dict, Any
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH

from .translator import HybridTranslator
from ..utils import get_logger, create_progress_tracker
from ..extraction import DocxExtractor

logger = get_logger(__name__)

class DocxTranslator:
    """Word文档翻译器"""
    
    def __init__(self, cloud_only: bool = False):
        self.translator = HybridTranslator(cloud_only=cloud_only)
        self.docx_extractor = DocxExtractor()
        mode = "仅云端模式" if cloud_only else "混合模式"
        logger.info(f"Word文档翻译器初始化完成，运行模式: {mode}")
    
    def translate_file(self, input_path: str, output_path: str) -> bool:
        """翻译单个Word文件"""
        try:
            logger.info(f"开始翻译Word文件: {os.path.basename(input_path)}")
            
            # 加载文档
            doc = Document(input_path)
            
            # 翻译文档内容
            self._translate_document(doc)
            
            # 保存翻译后的文档
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            doc.save(output_path)
            
            logger.info(f"Word文件翻译完成: {os.path.basename(output_path)}")
            return True
            
        except Exception as e:
            logger.error(f"翻译Word文件失败 {input_path}: {e}")
            return False
    
    def _translate_document(self, doc: Document):
        """翻译文档内容 - 批量优化版本"""
        # 收集所有需要翻译的文本
        all_texts = []
        text_locations = []

        # 1. 收集段落文本
        for para in doc.paragraphs:
            if para.text.strip():
                all_texts.append(para.text.strip())
                text_locations.append(('paragraph', para))

        # 2. 收集表格文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        if para.text.strip():
                            all_texts.append(para.text.strip())
                            text_locations.append(('table_cell', para))

        # 3. 收集页眉页脚文本
        for section in doc.sections:
            if section.header:
                for para in section.header.paragraphs:
                    if para.text.strip():
                        all_texts.append(para.text.strip())
                        text_locations.append(('header', para))

            if section.footer:
                for para in section.footer.paragraphs:
                    if para.text.strip():
                        all_texts.append(para.text.strip())
                        text_locations.append(('footer', para))

        # 4. 批量翻译所有文本
        if all_texts:
            logger.info(f"批量翻译 {len(all_texts)} 个文本片段")
            translated_texts = self.translator.translate_batch(all_texts)

            # 5. 应用翻译结果
            for i, (location_type, element) in enumerate(text_locations):
                if i < len(translated_texts):
                    self._apply_translation_to_element(element, translated_texts[i])

    def _apply_translation_to_element(self, element, translated_text):
        """将翻译结果应用到文档元素"""
        try:
            # 保存原始格式信息
            original_format = self._extract_format_info(element)

            # 清空并设置新文本
            element.clear()
            run = element.add_run(translated_text)

            # 恢复格式
            self._apply_format_info(run, original_format)

        except Exception as e:
            logger.error(f"应用翻译结果失败: {e}")

    def _extract_format_info(self, paragraph):
        """提取段落格式信息"""
        format_info = {
            'font_name': None,
            'font_size': None,
            'bold': False,
            'italic': False,
            'underline': False,
            'color': None
        }

        # 从第一个有效run提取格式
        for run in paragraph.runs:
            if run.text.strip():
                format_info['font_name'] = run.font.name
                format_info['font_size'] = run.font.size
                format_info['bold'] = run.font.bold
                format_info['italic'] = run.font.italic
                format_info['underline'] = run.font.underline
                format_info['color'] = run.font.color.rgb if run.font.color.rgb else None
                break

        return format_info

    def _apply_format_info(self, run, format_info):
        """应用格式信息"""
        try:
            if format_info['font_name']:
                run.font.name = format_info['font_name']
            if format_info['font_size']:
                run.font.size = format_info['font_size']
            if format_info['bold']:
                run.font.bold = format_info['bold']
            if format_info['italic']:
                run.font.italic = format_info['italic']
            if format_info['underline']:
                run.font.underline = format_info['underline']
            if format_info['color']:
                run.font.color.rgb = format_info['color']
        except Exception as e:
            logger.warning(f"应用格式时出错: {e}")
    

    
    def translate_folder(self, input_folder: str, output_folder: str) -> Dict[str, Any]:
        """批量翻译文件夹中的Word文件"""
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        
        # 扫描Word文件
        docx_files = list(input_path.glob("*.docx"))
        
        if not docx_files:
            logger.warning(f"在 {input_folder} 中没有找到Word文件")
            return {
                'total_files': 0,
                'success_files': 0,
                'failed_files': 0,
                'success_rate': 0.0
            }
        
        logger.info(f"找到 {len(docx_files)} 个Word文件")
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建进度跟踪器
        progress = create_progress_tracker(len(docx_files), "翻译Word文档")
        
        success_count = 0
        failed_count = 0
        
        for docx_file in docx_files:
            try:
                # 构建输出文件路径
                output_file = output_path / f"{docx_file.stem}_EN{docx_file.suffix}"
                
                # 翻译文件
                if self.translate_file(str(docx_file), str(output_file)):
                    success_count += 1
                    progress.update(docx_file.name, "success", "翻译完成")
                else:
                    failed_count += 1
                    progress.update(docx_file.name, "error", "翻译失败")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"处理文件 {docx_file.name} 时出错: {e}")
                progress.update(docx_file.name, "error", str(e))
        
        progress.finish()
        
        # 返回统计信息
        total_files = len(docx_files)
        success_rate = (success_count / total_files) * 100 if total_files > 0 else 0
        
        result = {
            'total_files': total_files,
            'success_files': success_count,
            'failed_files': failed_count,
            'success_rate': success_rate
        }
        
        logger.info(f"Word文档翻译完成: {success_count}/{total_files} 成功")
        return result
