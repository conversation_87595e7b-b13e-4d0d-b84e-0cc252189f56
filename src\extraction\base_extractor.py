#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础提取器
定义内容提取的通用接口和数据结构
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class ExtractedTable:
    """提取的表格数据"""
    page: int
    data: List[List[str]]
    headers: Optional[List[str]] = None
    position: Optional[Dict[str, float]] = None


@dataclass
class ExtractedParagraph:
    """提取的段落数据"""
    page: int
    text: str
    position: Optional[Dict[str, float]] = None
    style: Optional[str] = None


@dataclass
class ExtractedContent:
    """提取的内容数据结构"""
    title: str = ""
    product_name: str = ""
    tables: List[ExtractedTable] = None
    paragraphs: List[ExtractedParagraph] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tables is None:
            self.tables = []
        if self.paragraphs is None:
            self.paragraphs = []
        if self.metadata is None:
            self.metadata = {}


class BaseExtractor(ABC):
    """基础提取器抽象类"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    @abstractmethod
    def extract_content(self, file_path: str) -> Optional[ExtractedContent]:
        """
        提取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            ExtractedContent: 提取的内容，如果失败返回None
        """
        pass
    
    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """
        获取支持的文件扩展名
        
        Returns:
            List[str]: 支持的扩展名列表
        """
        pass
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否支持
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否支持
        """
        import os
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.get_supported_extensions()
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        if not text:
            return ""
        
        # 移除多余的空白字符
        import re
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除控制字符
        cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
        
        return cleaned
    
    def _extract_product_name_from_text(self, text: str) -> Optional[str]:
        """
        从文本中提取产品名称
        
        Args:
            text: 文本内容
            
        Returns:
            Optional[str]: 产品名称
        """
        import re
        
        # 产品名称模式
        patterns = [
            r'产品名称[：:]\s*([^\n\r]+)',
            r'产品型号[：:]\s*([^\n\r]+)',
            r'型号[：:]\s*([^\n\r]+)',
            r'Product[：:]\s*([^\n\r]+)',
            r'Model[：:]\s*([^\n\r]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                product_name = match.group(1).strip()
                if product_name:
                    return product_name
        
        return None
    
    def _is_valid_table_row(self, row: List[str]) -> bool:
        """
        检查表格行是否有效
        
        Args:
            row: 表格行数据
            
        Returns:
            bool: 是否有效
        """
        if not row:
            return False
        
        # 检查是否有非空内容
        has_content = any(cell and cell.strip() for cell in row)
        if not has_content:
            return False
        
        # 过滤明显的表头行
        first_cell = str(row[0]).strip().lower()
        header_indicators = ['序号', 'no', 'number', '项目', 'item', '参数', 'parameter']
        if first_cell in header_indicators:
            return False
        
        return True
    
    def _is_valid_paragraph(self, text: str) -> bool:
        """
        检查段落是否有效
        
        Args:
            text: 段落文本
            
        Returns:
            bool: 是否有效
        """
        if not text or len(text.strip()) < 2:
            return False
        
        # 过滤页眉页脚等无用信息
        useless_patterns = [
            r'^\d+$',  # 纯数字（页码）
            r'^第\s*\d+\s*页',  # 页码信息
            r'^\s*[-=_]{3,}\s*$',  # 分隔线
        ]
        
        for pattern in useless_patterns:
            if re.match(pattern, text.strip()):
                return False
        
        return True
    
    def get_extraction_summary(self, content: ExtractedContent) -> Dict[str, Any]:
        """
        获取提取摘要信息
        
        Args:
            content: 提取的内容
            
        Returns:
            Dict[str, Any]: 摘要信息
        """
        return {
            'title': content.title,
            'product_name': content.product_name,
            'table_count': len(content.tables),
            'paragraph_count': len(content.paragraphs),
            'total_table_rows': sum(len(table.data) for table in content.tables),
            'total_text_length': sum(len(para.text) for para in content.paragraphs),
            'metadata_keys': list(content.metadata.keys()) if content.metadata else []
        }
