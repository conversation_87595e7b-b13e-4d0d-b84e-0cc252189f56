#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模糊匹配模块
使用模糊匹配算法提高参数名称匹配准确率
"""

from typing import Dict, List, Tuple, Optional, Any
from rapidfuzz import fuzz, process
from ..utils import get_logger, get_config_manager

logger = get_logger(__name__)

class FuzzyMatcher:
    """模糊匹配器"""
    
    def __init__(self, threshold: float = 85.0):
        self.threshold = threshold
        self.config_manager = get_config_manager()

        # 获取参数映射（包含学习的映射）
        self.param_mapping = self.config_manager.get_param_mapping()

        # 构建反向索引：关键词 -> 参数名
        self._rebuild_keyword_index()

        logger.debug(f"模糊匹配器初始化完成，阈值: {threshold}，关键词数量: {len(self.all_keywords)}")

    def _rebuild_keyword_index(self):
        """重建关键词索引"""
        self.keyword_to_param = {}
        for param_name, keywords in self.param_mapping.items():
            for keyword in keywords:
                self.keyword_to_param[keyword.lower()] = param_name

        # 所有可能的关键词列表
        self.all_keywords = list(self.keyword_to_param.keys())
        logger.debug(f"重建关键词索引完成，关键词数量: {len(self.all_keywords)}")
    
    def match_parameter(self, extracted_param: str, tech_params: Dict[str, Any]) -> Optional[str]:
        """匹配参数名称"""
        if not extracted_param or not extracted_param.strip():
            return None
        
        extracted_param = extracted_param.strip()
        
        # 1. 精确匹配
        exact_match = self._exact_match(extracted_param)
        if exact_match:
            logger.debug(f"精确匹配: {extracted_param} -> {exact_match}")
            return exact_match
        
        # 2. 模糊匹配
        fuzzy_match = self._fuzzy_match(extracted_param)
        if fuzzy_match:
            param_name, confidence = fuzzy_match
            logger.debug(f"模糊匹配: {extracted_param} -> {param_name} (置信度: {confidence:.1f}%)")
            
            # 学习新的映射关系
            self._learn_mapping(param_name, extracted_param, confidence / 100.0)
            return param_name
        
        # 3. 关键词匹配
        keyword_match = self._keyword_match(extracted_param)
        if keyword_match:
            logger.debug(f"关键词匹配: {extracted_param} -> {keyword_match}")
            return keyword_match
        
        # 4. 部分匹配
        partial_match = self._partial_match(extracted_param)
        if partial_match:
            param_name, confidence = partial_match
            logger.debug(f"部分匹配: {extracted_param} -> {param_name} (置信度: {confidence:.1f}%)")
            return param_name
        
        logger.warning(f"未找到匹配: {extracted_param}")
        return None
    
    def _exact_match(self, param: str) -> Optional[str]:
        """精确匹配"""
        param_lower = param.lower()
        
        # 直接查找
        if param_lower in self.keyword_to_param:
            return self.keyword_to_param[param_lower]
        
        # 去除常见后缀再查找
        suffixes = ['(a)', '(b)', 'a', 'b', '值', '度', '率', '性', '能']
        for suffix in suffixes:
            if param_lower.endswith(suffix):
                clean_param = param_lower[:-len(suffix)]
                if clean_param in self.keyword_to_param:
                    return self.keyword_to_param[clean_param]
        
        return None
    
    def _fuzzy_match(self, param: str) -> Optional[Tuple[str, float]]:
        """模糊匹配"""
        param_lower = param.lower()
        
        # 使用rapidfuzz进行模糊匹配
        result = process.extractOne(
            param_lower,
            self.all_keywords,
            scorer=fuzz.WRatio,
            score_cutoff=self.threshold
        )
        
        if result:
            matched_keyword, confidence, _ = result
            param_name = self.keyword_to_param[matched_keyword]
            return param_name, confidence
        
        return None
    
    def _keyword_match(self, param: str) -> Optional[str]:
        """关键词匹配"""
        param_lower = param.lower()
        
        # 检查是否包含任何关键词
        for keyword, param_name in self.keyword_to_param.items():
            if keyword in param_lower or param_lower in keyword:
                return param_name
        
        return None
    
    def _partial_match(self, param: str) -> Optional[Tuple[str, float]]:
        """部分匹配（降低阈值）"""
        param_lower = param.lower()
        
        # 使用较低的阈值进行匹配
        lower_threshold = max(60.0, self.threshold - 20)
        
        result = process.extractOne(
            param_lower,
            self.all_keywords,
            scorer=fuzz.partial_ratio,
            score_cutoff=lower_threshold
        )
        
        if result:
            matched_keyword, confidence, _ = result
            param_name = self.keyword_to_param[matched_keyword]
            return param_name, confidence
        
        return None
    
    def _learn_mapping(self, param_name: str, matched_keyword: str, confidence: float):
        """学习新的映射关系"""
        try:
            self.config_manager.learn_mapping(param_name, matched_keyword, confidence)

            # 更新本地映射
            if param_name not in self.param_mapping:
                self.param_mapping[param_name] = []

            if matched_keyword not in self.param_mapping[param_name]:
                self.param_mapping[param_name].append(matched_keyword)

                # 重建关键词索引以包含新学习的映射
                self._rebuild_keyword_index()
                logger.debug(f"学习映射后重建索引，新关键词数量: {len(self.all_keywords)}")

        except Exception as e:
            logger.error(f"学习映射时出错: {e}")
    
    def match_value_for_ab_components(self, tech_params: Dict[str, Any],
                                    target_param: str) -> Optional[str]:
        """为A/B组分匹配值"""
        if target_param not in self.param_mapping:
            return None

        keywords = self.param_mapping[target_param]

        # 特殊处理A/B组分 - 只在常规性能参数中查找
        if target_param.endswith('(A)'):
            base_param = target_param[:-3]
            # 只查找A/B组分相关的参数（常规性能）
            ab_component_params = ['外观', '粘度', '密度', '保存期限']
            if any(comp in base_param for comp in ab_component_params):
                for keyword in keywords:
                    if f"{keyword}(A)" in tech_params:
                        return tech_params[f"{keyword}(A)"]
                    # 尝试不同的A组分标识
                    for param_key in tech_params.keys():
                        if keyword in param_key and ('A' in param_key and 'B' not in param_key):
                            return tech_params[param_key]
        elif target_param.endswith('(B)'):
            base_param = target_param[:-3]
            # 只查找A/B组分相关的参数（常规性能）
            ab_component_params = ['外观', '粘度', '密度', '保存期限']
            if any(comp in base_param for comp in ab_component_params):
                for keyword in keywords:
                    if f"{keyword}(B)" in tech_params:
                        return tech_params[f"{keyword}(B)"]
                    # 尝试不同的B组分标识
                    for param_key in tech_params.keys():
                        if keyword in param_key and ('B' in param_key and 'A' not in param_key):
                            return tech_params[param_key]
        else:
            # 普通参数匹配（非A/B组分）
            for keyword in keywords:
                if keyword in tech_params:
                    return tech_params[keyword]

                # 模糊匹配现有参数
                for param_key in tech_params.keys():
                    if self._is_similar(keyword, param_key):
                        return tech_params[param_key]

        return None
    
    def _is_similar(self, keyword: str, param_key: str, threshold: float = 80.0) -> bool:
        """检查两个字符串是否相似"""
        similarity = fuzz.WRatio(keyword.lower(), param_key.lower())
        return similarity >= threshold
    
    def get_match_suggestions(self, param: str, limit: int = 5) -> List[Tuple[str, float]]:
        """获取匹配建议"""
        param_lower = param.lower()
        
        results = process.extract(
            param_lower,
            self.all_keywords,
            scorer=fuzz.WRatio,
            limit=limit
        )
        
        suggestions = []
        for matched_keyword, confidence, _ in results:
            param_name = self.keyword_to_param[matched_keyword]
            suggestions.append((param_name, confidence))
        
        return suggestions
    
    def update_threshold(self, new_threshold: float):
        """更新匹配阈值"""
        self.threshold = new_threshold
        logger.info(f"模糊匹配阈值已更新为: {new_threshold}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取匹配统计信息"""
        return {
            'total_keywords': len(self.all_keywords),
            'total_params': len(self.param_mapping),
            'threshold': self.threshold,
            'learned_mappings': len(self.config_manager.learned_mappings.get('learned_mappings', {}))
        }
    
    def export_learned_mappings(self) -> Dict[str, List[str]]:
        """导出学习的映射关系"""
        return self.config_manager.learned_mappings.get('learned_mappings', {})
    
    def import_learned_mappings(self, mappings: Dict[str, List[str]]):
        """导入学习的映射关系"""
        try:
            current_mappings = self.config_manager.learned_mappings.get('learned_mappings', {})
            
            for param_name, keywords in mappings.items():
                if param_name not in current_mappings:
                    current_mappings[param_name] = []
                
                for keyword in keywords:
                    if keyword not in current_mappings[param_name]:
                        current_mappings[param_name].append(keyword)
                        self.keyword_to_param[keyword.lower()] = param_name
                        if keyword.lower() not in self.all_keywords:
                            self.all_keywords.append(keyword.lower())
            
            self.config_manager.learned_mappings['learned_mappings'] = current_mappings
            self.config_manager.save_learned_mappings()
            
            logger.info(f"成功导入 {len(mappings)} 个映射关系")
            
        except Exception as e:
            logger.error(f"导入映射关系时出错: {e}")

# 全局模糊匹配器实例
_fuzzy_matcher = None

def get_fuzzy_matcher() -> FuzzyMatcher:
    """获取模糊匹配器实例"""
    global _fuzzy_matcher
    if _fuzzy_matcher is None:
        config_manager = get_config_manager()
        threshold = config_manager.get_setting('fuzzy_match_threshold', 85.0)
        _fuzzy_matcher = FuzzyMatcher(threshold)
    return _fuzzy_matcher
