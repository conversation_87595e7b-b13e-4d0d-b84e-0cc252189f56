#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一翻译模块
基于第一性原则重构的翻译系统，提供完整的文档翻译功能
"""

# 核心翻译引擎
from .unified_translator import UnifiedTranslator
from .unified_document_processor import UnifiedDocumentProcessor
from .translation_main import TranslationSystem

# 组件模块
from .translator import HybridTranslator, HelsinkiTranslator
from .technical_dictionary import TechnicalDictionary, get_technical_dictionary
from .pdf_translator import PDFTranslator
from .docx_translator import DocxTranslator
from .smart_segmenter import SmartSegmenter
from .quality_checker import QualityChecker
from .learning_advisor import LearningAdvisor

__all__ = [
    # 统一翻译系统（推荐使用）
    'UnifiedTranslator',
    'UnifiedDocumentProcessor',
    'TranslationSystem',

    # 组件模块
    'HybridTranslator',
    'HelsinkiTranslator',
    'TechnicalDictionary',
    'get_technical_dictionary',
    'PDFTranslator',
    'DocxTranslator',
    'SmartSegmenter',
    'QualityChecker',
    'LearningAdvisor'
]
