#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度显示模块
提供文件处理进度条和状态显示
"""

import time
from tqdm import tqdm
from typing import List, Optional
from .logger import get_logger

logger = get_logger(__name__)

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total_files: int, description: str = "处理文件"):
        self.total_files = total_files
        self.description = description
        self.start_time = time.time()
        self.processed_files = 0
        self.successful_files = 0
        self.failed_files = 0
        
        # 创建进度条
        self.pbar = tqdm(
            total=total_files,
            desc=description,
            unit="文件",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]"
        )
        
        logger.info(f"开始处理 {total_files} 个文件")
    
    def update(self, filename: str, status: str = "success", message: str = ""):
        """更新进度"""
        self.processed_files += 1
        
        if status == "success":
            self.successful_files += 1
            status_icon = "✅"
        elif status == "error":
            self.failed_files += 1
            status_icon = "❌"
        elif status == "warning":
            self.successful_files += 1
            status_icon = "⚠️"
        else:
            status_icon = "📄"
        
        # 更新进度条描述
        current_desc = f"{self.description} {status_icon} {filename[:30]}..."
        if len(filename) <= 30:
            current_desc = f"{self.description} {status_icon} {filename}"
        
        self.pbar.set_description(current_desc)
        self.pbar.update(1)
        
        # 更新后缀信息
        success_rate = (self.successful_files / self.processed_files) * 100 if self.processed_files > 0 else 0
        self.pbar.set_postfix({
            "成功": self.successful_files,
            "失败": self.failed_files,
            "成功率": f"{success_rate:.1f}%"
        })
        
        if message:
            logger.debug(f"处理 {filename}: {message}")
    
    def finish(self):
        """完成处理"""
        self.pbar.close()
        
        total_time = time.time() - self.start_time
        avg_time = total_time / self.processed_files if self.processed_files > 0 else 0
        
        logger.info("=" * 60)
        logger.info("处理完成统计:")
        logger.info(f"  总文件数: {self.total_files}")
        logger.info(f"  成功处理: {self.successful_files}")
        logger.info(f"  处理失败: {self.failed_files}")
        logger.info(f"  成功率: {(self.successful_files/self.total_files)*100:.1f}%")
        logger.info(f"  总耗时: {total_time:.2f}秒")
        logger.info(f"  平均耗时: {avg_time:.2f}秒/文件")
        logger.info("=" * 60)
    
    def get_statistics(self):
        """获取统计信息"""
        total_time = time.time() - self.start_time
        return {
            "total_files": self.total_files,
            "processed_files": self.processed_files,
            "successful_files": self.successful_files,
            "failed_files": self.failed_files,
            "success_rate": (self.successful_files / self.processed_files) * 100 if self.processed_files > 0 else 0,
            "total_time": total_time,
            "avg_time": total_time / self.processed_files if self.processed_files > 0 else 0
        }

class BatchProgressTracker:
    """批量处理进度跟踪器"""
    
    def __init__(self, batches: List[str]):
        self.batches = batches
        self.current_batch = 0
        self.batch_trackers = {}
        
        logger.info(f"开始批量处理 {len(batches)} 个批次")
    
    def start_batch(self, batch_name: str, file_count: int):
        """开始新批次"""
        self.current_batch += 1
        logger.info(f"开始批次 {self.current_batch}/{len(self.batches)}: {batch_name}")
        
        tracker = ProgressTracker(
            file_count, 
            f"批次{self.current_batch}/{len(self.batches)} - {batch_name}"
        )
        self.batch_trackers[batch_name] = tracker
        return tracker
    
    def finish_batch(self, batch_name: str):
        """完成批次"""
        if batch_name in self.batch_trackers:
            self.batch_trackers[batch_name].finish()
    
    def get_overall_statistics(self):
        """获取总体统计"""
        total_stats = {
            "total_files": 0,
            "successful_files": 0,
            "failed_files": 0,
            "total_time": 0
        }
        
        for tracker in self.batch_trackers.values():
            stats = tracker.get_statistics()
            total_stats["total_files"] += stats["total_files"]
            total_stats["successful_files"] += stats["successful_files"]
            total_stats["failed_files"] += stats["failed_files"]
            total_stats["total_time"] += stats["total_time"]
        
        if total_stats["total_files"] > 0:
            total_stats["success_rate"] = (total_stats["successful_files"] / total_stats["total_files"]) * 100
            total_stats["avg_time"] = total_stats["total_time"] / total_stats["total_files"]
        
        return total_stats

def create_progress_tracker(total_files: int, description: str = "处理文件") -> ProgressTracker:
    """创建进度跟踪器的便捷函数"""
    return ProgressTracker(total_files, description)
