#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF翻译器
保持原始格式的PDF翻译
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Tuple
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

from .translator import HybridTranslator
from ..utils import get_logger, create_progress_tracker
from ..extraction import PDFExtractor

logger = get_logger(__name__)

class PDFTranslator:
    """PDF翻译器"""
    
    def __init__(self, cloud_only: bool = False):
        self.translator = HybridTranslator(cloud_only=cloud_only)
        self.pdf_extractor = PDFExtractor()
        self._setup_fonts()
        mode = "仅云端模式" if cloud_only else "混合模式"
        logger.info(f"PDF翻译器初始化完成，运行模式: {mode}")
    
    def _setup_fonts(self):
        """设置字体"""
        try:
            # 注册中文字体（如果需要显示中文）
            # 这里使用系统默认字体
            self.styles = getSampleStyleSheet()
            
            # 创建英文样式
            self.title_style = ParagraphStyle(
                'CustomTitle',
                parent=self.styles['Title'],
                fontSize=16,
                spaceAfter=12,
                alignment=1  # 居中
            )
            
            self.normal_style = ParagraphStyle(
                'CustomNormal', 
                parent=self.styles['Normal'],
                fontSize=10,
                spaceAfter=6
            )
            
        except Exception as e:
            logger.warning(f"字体设置失败: {e}")
    
    def translate_file(self, input_path: str, output_path: str) -> bool:
        """翻译单个PDF文件"""
        try:
            logger.info(f"开始翻译PDF文件: {os.path.basename(input_path)}")
            
            # 提取PDF内容
            content = self._extract_pdf_content(input_path)
            
            if not content:
                logger.warning(f"PDF文件内容为空: {input_path}")
                return False
            
            # 翻译内容
            translated_content = self._translate_content(content)
            
            # 生成新PDF
            self._create_translated_pdf(translated_content, output_path)
            
            logger.info(f"PDF文件翻译完成: {os.path.basename(output_path)}")
            return True
            
        except Exception as e:
            logger.error(f"翻译PDF文件失败 {input_path}: {e}")
            return False
    
    def _extract_pdf_content(self, pdf_path: str) -> Dict[str, Any]:
        """提取PDF内容"""
        try:
            # 使用统一的PDF提取器
            extracted_content = self.pdf_extractor.extract_content(pdf_path)

            if not extracted_content:
                logger.warning(f"PDF内容提取为空: {pdf_path}")
                return {
                    'title': '',
                    'tables': [],
                    'paragraphs': [],
                    'metadata': {}
                }

            # 转换为旧格式以兼容现有代码
            content = {
                'title': extracted_content.title,
                'tables': [],
                'paragraphs': [],
                'metadata': extracted_content.metadata or {}
            }

            # 转换表格格式
            for table in extracted_content.tables:
                content['tables'].append({
                    'page': table.page,
                    'data': table.data
                })

            # 转换段落格式
            for para in extracted_content.paragraphs:
                content['paragraphs'].append({
                    'page': para.page,
                    'text': para.text
                })

            return content

        except Exception as e:
            logger.error(f"PDF内容提取失败: {e}")
            raise
    
    def _translate_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """翻译内容 - 批量优化版本"""
        translated_content = {
            'title': '',
            'tables': [],
            'paragraphs': []
        }

        try:
            # 收集所有需要翻译的文本
            all_texts = []
            text_mappings = []

            # 1. 收集标题
            if content['title']:
                all_texts.append(content['title'])
                text_mappings.append(('title', 0))

            # 2. 收集段落文本
            for i, para in enumerate(content['paragraphs']):
                all_texts.append(para['text'])
                text_mappings.append(('paragraph', i))

            # 3. 收集表格文本
            table_cell_texts = []
            for table_idx, table_info in enumerate(content['tables']):
                for row_idx, row in enumerate(table_info['data']):
                    for cell_idx, cell in enumerate(row):
                        if cell and cell.strip() and not self._is_numeric_or_unit(cell):
                            all_texts.append(cell.strip())
                            text_mappings.append(('table_cell', table_idx, row_idx, cell_idx))

            # 4. 批量翻译
            if all_texts:
                logger.info(f"批量翻译 {len(all_texts)} 个文本片段")
                translated_texts = self.translator.translate_batch(all_texts)

                # 5. 应用翻译结果
                result_idx = 0

                # 初始化结构
                translated_content['paragraphs'] = [
                    {'page': para['page'], 'text': ''} for para in content['paragraphs']
                ]
                translated_content['tables'] = [
                    {'page': table['page'], 'data': [row[:] for row in table['data']]}
                    for table in content['tables']
                ]

                # 应用翻译结果
                for mapping in text_mappings:
                    if result_idx < len(translated_texts):
                        translated_text = translated_texts[result_idx]

                        if mapping[0] == 'title':
                            translated_content['title'] = translated_text
                        elif mapping[0] == 'paragraph':
                            para_idx = mapping[1]
                            translated_content['paragraphs'][para_idx]['text'] = translated_text
                        elif mapping[0] == 'table_cell':
                            table_idx, row_idx, cell_idx = mapping[1], mapping[2], mapping[3]
                            translated_content['tables'][table_idx]['data'][row_idx][cell_idx] = translated_text

                        result_idx += 1

        except Exception as e:
            logger.error(f"翻译内容失败: {e}")
            raise

        return translated_content
    

    
    def _is_numeric_or_unit(self, text: str) -> bool:
        """判断是否为数值或单位"""
        import re
        
        # 数值模式
        numeric_patterns = [
            r'^\d+(\.\d+)?$',  # 纯数字
            r'^\d+(\.\d+)?\s*[A-Za-z·℃%]+$',  # 数字+单位
            r'^[>≥<≤]\s*\d+',  # 大于小于符号
            r'^\d+:\d+$',  # 比例
            r'^\d+±\d+$',  # 误差范围
            r'^-?\d+~\d+$',  # 范围
        ]
        
        for pattern in numeric_patterns:
            if re.match(pattern, text.strip()):
                return True
        
        return False
    
    def _create_translated_pdf(self, content: Dict[str, Any], output_path: str):
        """创建翻译后的PDF"""
        try:
            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 创建PDF文档
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # 构建内容
            story = []
            
            # 添加标题
            if content['title']:
                title_para = Paragraph(content['title'], self.title_style)
                story.append(title_para)
                story.append(Spacer(1, 12))
            
            # 添加段落
            for para in content['paragraphs']:
                if para['text'].strip():
                    p = Paragraph(para['text'], self.normal_style)
                    story.append(p)
                    story.append(Spacer(1, 6))
            
            # 添加表格
            for table_info in content['tables']:
                if table_info['data']:
                    table = Table(table_info['data'])
                    
                    # 设置表格样式
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 10),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 9),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(table)
                    story.append(Spacer(1, 12))
            
            # 生成PDF
            doc.build(story)
            
        except Exception as e:
            logger.error(f"创建PDF失败: {e}")
            raise
    
    def translate_folder(self, input_folder: str, output_folder: str) -> Dict[str, Any]:
        """批量翻译文件夹中的PDF文件"""
        input_path = Path(input_folder)
        output_path = Path(output_folder)
        
        # 扫描PDF文件
        pdf_files = list(input_path.glob("*.pdf"))
        
        if not pdf_files:
            logger.warning(f"在 {input_folder} 中没有找到PDF文件")
            return {
                'total_files': 0,
                'success_files': 0,
                'failed_files': 0,
                'success_rate': 0.0
            }
        
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建进度跟踪器
        progress = create_progress_tracker(len(pdf_files), "翻译PDF文档")
        
        success_count = 0
        failed_count = 0
        
        for pdf_file in pdf_files:
            try:
                # 构建输出文件路径
                output_file = output_path / f"{pdf_file.stem}_EN{pdf_file.suffix}"
                
                # 翻译文件
                if self.translate_file(str(pdf_file), str(output_file)):
                    success_count += 1
                    progress.update(pdf_file.name, "success", "翻译完成")
                else:
                    failed_count += 1
                    progress.update(pdf_file.name, "error", "翻译失败")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"处理文件 {pdf_file.name} 时出错: {e}")
                progress.update(pdf_file.name, "error", str(e))
        
        progress.finish()
        
        # 返回统计信息
        total_files = len(pdf_files)
        success_rate = (success_count / total_files) * 100 if total_files > 0 else 0
        
        result = {
            'total_files': total_files,
            'success_files': success_count,
            'failed_files': failed_count,
            'success_rate': success_rate
        }
        
        logger.info(f"PDF文档翻译完成: {success_count}/{total_files} 成功")
        return result
