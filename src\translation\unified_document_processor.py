#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一文档处理器
基于统一翻译引擎的简化文档处理系统
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from ..utils import get_logger, create_progress_tracker

logger = get_logger(__name__)


@dataclass
class DocumentProcessingResult:
    """文档处理结果"""
    input_file: str
    output_file: str
    success: bool
    total_segments: int
    success_rate: float
    processing_time: float
    error_message: str = ""
    learning_suggestions_count: int = 0


class UnifiedDocumentProcessor:
    """
    统一文档处理器
    
    功能：
    1. 统一处理PDF和Word文档
    2. 使用统一翻译引擎进行翻译
    3. 保留原始格式
    4. 生成学习报告
    5. 批量处理支持
    """
    
    def __init__(self, cloud_only: bool = False):
        self.logger = get_logger(__name__)
        self.cloud_only = cloud_only
        
        # 初始化统一翻译引擎
        from .unified_translator import UnifiedTranslator
        self.translator = UnifiedTranslator({'cloud_only': cloud_only})
        
        # 初始化文档处理器
        self._init_document_processors()
        
        mode = "仅云端模式" if cloud_only else "混合模式"
        self.logger.info(f"统一文档处理器初始化完成，运行模式: {mode}")
    
    def _init_document_processors(self):
        """初始化文档处理器"""
        try:
            # PDF处理器
            from .pdf_translator import PDFTranslator
            self.pdf_processor = PDFTranslator(cloud_only=self.cloud_only)
            
            # Word处理器
            from .docx_translator import DocxTranslator
            self.docx_processor = DocxTranslator(cloud_only=self.cloud_only)
            
        except Exception as e:
            self.logger.error(f"文档处理器初始化失败: {e}")
            raise
    
    def process_single_file(self, input_file: str, output_file: str = None, 
                          generate_report: bool = True) -> DocumentProcessingResult:
        """
        处理单个文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径（可选）
            generate_report: 是否生成学习报告
            
        Returns:
            DocumentProcessingResult: 处理结果
        """
        import time
        start_time = time.time()
        
        input_path = Path(input_file)
        
        if not input_path.exists():
            return DocumentProcessingResult(
                input_file=input_file,
                output_file="",
                success=False,
                total_segments=0,
                success_rate=0.0,
                processing_time=0.0,
                error_message="文件不存在"
            )
        
        # 确定输出文件路径
        if output_file is None:
            output_file = str(input_path.parent / f"{input_path.stem}_EN{input_path.suffix}")
        
        try:
            # 根据文件类型选择处理方法
            if input_path.suffix.lower() == '.pdf':
                success = self._process_pdf_file(input_file, output_file)
            elif input_path.suffix.lower() == '.docx':
                success = self._process_docx_file(input_file, output_file)
            else:
                return DocumentProcessingResult(
                    input_file=input_file,
                    output_file=output_file,
                    success=False,
                    total_segments=0,
                    success_rate=0.0,
                    processing_time=time.time() - start_time,
                    error_message=f"不支持的文件格式: {input_path.suffix}"
                )
            
            processing_time = time.time() - start_time
            
            # 生成处理结果（这里简化处理，实际应该从翻译上下文获取详细信息）
            result = DocumentProcessingResult(
                input_file=input_file,
                output_file=output_file,
                success=success,
                total_segments=0,  # 需要从实际处理中获取
                success_rate=0.0,  # 需要从实际处理中获取
                processing_time=processing_time
            )
            
            if generate_report and success:
                # 生成学习报告（保存到同目录下）
                report_file = str(input_path.parent / f"{input_path.stem}_learning_report.txt")
                self._generate_file_report(input_file, report_file)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理文件失败 {input_file}: {e}")
            return DocumentProcessingResult(
                input_file=input_file,
                output_file=output_file,
                success=False,
                total_segments=0,
                success_rate=0.0,
                processing_time=time.time() - start_time,
                error_message=str(e)
            )
    
    def _process_pdf_file(self, input_file: str, output_file: str) -> bool:
        """处理PDF文件"""
        try:
            return self.pdf_processor.translate_file(input_file, output_file)
        except Exception as e:
            self.logger.error(f"PDF处理失败: {e}")
            return False
    
    def _process_docx_file(self, input_file: str, output_file: str) -> bool:
        """处理Word文件"""
        try:
            return self.docx_processor.translate_file(input_file, output_file)
        except Exception as e:
            self.logger.error(f"Word处理失败: {e}")
            return False
    
    def process_folder(self, input_folder: str, output_folder: str = None, 
                      generate_reports: bool = True) -> Dict[str, Any]:
        """
        批量处理文件夹
        
        Args:
            input_folder: 输入文件夹路径
            output_folder: 输出文件夹路径（可选）
            generate_reports: 是否生成学习报告
            
        Returns:
            Dict[str, Any]: 批量处理结果
        """
        input_path = Path(input_folder)
        
        if not input_path.exists():
            self.logger.error(f"输入文件夹不存在: {input_folder}")
            return {'success': False, 'error': '输入文件夹不存在'}
        
        # 确定输出文件夹
        if output_folder is None:
            output_folder = str(input_path.parent / f"{input_path.name}_EN")
        
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找支持的文件
        supported_files = self._find_supported_files(input_path)
        
        if not supported_files:
            self.logger.warning(f"在 {input_folder} 中未找到支持的文件")
            return {
                'success': True,
                'total_files': 0,
                'success_files': 0,
                'failed_files': 0,
                'results': []
            }
        
        # 创建进度跟踪器
        progress = create_progress_tracker(len(supported_files), "处理文档")
        
        results = []
        success_count = 0
        failed_count = 0
        
        for file_path in supported_files:
            try:
                # 构建输出文件路径
                relative_path = file_path.relative_to(input_path)
                output_file = output_path / f"{relative_path.stem}_EN{relative_path.suffix}"
                output_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 处理文件
                result = self.process_single_file(
                    str(file_path), 
                    str(output_file), 
                    generate_reports
                )
                
                results.append(result)
                
                if result.success:
                    success_count += 1
                    progress.update(file_path.name, "success", "处理完成")
                else:
                    failed_count += 1
                    progress.update(file_path.name, "error", result.error_message)
                    
            except Exception as e:
                failed_count += 1
                self.logger.error(f"处理文件 {file_path.name} 时出错: {e}")
                progress.update(file_path.name, "error", str(e))
                
                results.append(DocumentProcessingResult(
                    input_file=str(file_path),
                    output_file="",
                    success=False,
                    total_segments=0,
                    success_rate=0.0,
                    processing_time=0.0,
                    error_message=str(e)
                ))
        
        # 生成批量处理报告
        if generate_reports and results:
            self._generate_batch_report(results, output_path)
        
        return {
            'success': True,
            'total_files': len(supported_files),
            'success_files': success_count,
            'failed_files': failed_count,
            'results': results,
            'output_folder': output_folder
        }
    
    def _find_supported_files(self, folder_path: Path) -> List[Path]:
        """查找支持的文件"""
        supported_extensions = {'.pdf', '.docx'}
        supported_files = []
        
        for file_path in folder_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                supported_files.append(file_path)
        
        return supported_files
    
    def _generate_file_report(self, input_file: str, report_file: str):
        """为单个文件生成学习报告"""
        try:
            # 这里应该从实际的翻译上下文生成报告
            # 由于当前架构限制，这里生成一个简化的报告
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"翻译学习报告\n")
                f.write(f"=" * 40 + "\n")
                f.write(f"源文件: {input_file}\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"\n注意: 详细的学习建议需要在翻译过程中实时生成\n")
                
        except Exception as e:
            self.logger.error(f"生成学习报告失败: {e}")
    
    def _generate_batch_report(self, results: List[DocumentProcessingResult], output_path: Path):
        """生成批量处理报告"""
        try:
            report_file = output_path / "batch_processing_report.txt"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("批量文档处理报告\n")
                f.write("=" * 50 + "\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总文件数: {len(results)}\n")
                
                success_results = [r for r in results if r.success]
                failed_results = [r for r in results if not r.success]
                
                f.write(f"成功文件数: {len(success_results)}\n")
                f.write(f"失败文件数: {len(failed_results)}\n")
                f.write(f"成功率: {len(success_results)/len(results):.1%}\n")
                
                if failed_results:
                    f.write(f"\n失败文件列表:\n")
                    for result in failed_results:
                        f.write(f"  - {result.input_file}: {result.error_message}\n")
                
                # 统计信息
                total_processing_time = sum(r.processing_time for r in results)
                f.write(f"\n总处理时间: {total_processing_time:.2f} 秒\n")
                f.write(f"平均处理时间: {total_processing_time/len(results):.2f} 秒/文件\n")
                
        except Exception as e:
            self.logger.error(f"生成批量报告失败: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        return ['.pdf', '.docx']
    
    def translate_text_directly(self, text: str, format_type: str = 'detailed') -> str:
        """直接翻译文本（用于测试和调试）"""
        context = self.translator.translate_text(text)
        return self.translator.format_output(context, format_type)
