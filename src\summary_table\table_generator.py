#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汇总表生成器
生成包含所有产品技术参数的Excel汇总表
"""

import os
from pathlib import Path
from typing import List, Dict, Any
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from ..utils import get_logger

logger = get_logger(__name__)


class SummaryTableGenerator:
    """汇总表生成器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # 获取配置
        self.column_mapping = config.get_column_mapping()
        self.settings = config.get_settings()
        
        # 表格样式配置
        self.header_font = Font(name='微软雅黑', size=11, bold=True)
        self.data_font = Font(name='微软雅黑', size=10)
        self.header_fill = PatternFill(start_color='E6F3FF', end_color='E6F3FF', fill_type='solid')
        self.ab_fill_a = PatternFill(start_color='FFE6E6', end_color='FFE6E6', fill_type='solid')
        self.ab_fill_b = PatternFill(start_color='E6FFE6', end_color='E6FFE6', fill_type='solid')
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        self.logger.info("汇总表生成器初始化完成")
    
    def generate_summary_table(self, data_list: List[Dict[str, Any]], output_file: str = None):
        """生成汇总表"""
        try:
            if not data_list:
                self.logger.error("没有数据可生成汇总表")
                return False
            
            output_file = output_file or self.config.get_output_file()

            # 如果输出文件没有路径，使用当前目录
            if not os.path.dirname(output_file):
                output_file = os.path.join(".", output_file)

            self.logger.info(f"开始生成汇总表，包含 {len(data_list)} 个产品")
            self.logger.info(f"输出文件路径: {output_file}")

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "技术参数汇总表"

            # 设置表头和A/B标识
            self._setup_headers(ws)

            # 填充数据
            self._fill_data(ws, data_list)

            # 应用样式
            self._apply_styles(ws, len(data_list))

            # 调整列宽
            self._adjust_column_widths(ws)

            # 确保输出目录存在
            output_dir = os.path.dirname(output_file)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 保存文件
            wb.save(output_file)
            
            self.logger.info(f"汇总表生成完成: {output_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"汇总表生成失败: {e}")
            return False
    
    def _setup_headers(self, ws):
        """设置表头和A/B标识"""
        try:
            header_row = self.config.get_header_row()
            ab_row = self.config.get_ab_indicator_row()
            
            # 获取列顺序
            column_order = self.config.get_column_order()
            
            for i, col_letter in enumerate(column_order, 1):
                param_name = self.column_mapping[col_letter]
                
                # 设置表头
                ws.cell(row=header_row, column=i, value=param_name)
                
                # 设置A/B标识
                if "(A)" in param_name:
                    ws.cell(row=ab_row, column=i, value="A")
                elif "(B)" in param_name:
                    ws.cell(row=ab_row, column=i, value="B")
                else:
                    ws.cell(row=ab_row, column=i, value="")
            
            self.logger.debug(f"表头设置完成，共 {len(column_order)} 列")
            
        except Exception as e:
            self.logger.error(f"表头设置失败: {e}")
    
    def _fill_data(self, ws, data_list: List[Dict[str, Any]]):
        """填充数据"""
        try:
            data_start_row = self.config.get_data_start_row()
            column_order = self.config.get_column_order()
            
            for row_idx, data in enumerate(data_list):
                excel_row = data_start_row + row_idx
                
                # 填充序号
                ws.cell(row=excel_row, column=1, value=row_idx + 1)
                
                # 填充产品型号
                product_model = data.get('product_model', '') or data.get('product_name', '')
                ws.cell(row=excel_row, column=2, value=product_model)
                
                # 填充参数数据
                parameters = data.get('parameters', {})
                
                for col_idx, col_letter in enumerate(column_order, 1):
                    param_name = self.column_mapping[col_letter]
                    
                    # 跳过序号和产品型号列
                    if param_name in ["序号", "产品型号"]:
                        continue
                    
                    # 获取参数值
                    param_value = parameters.get(param_name, "")
                    
                    # 填充到Excel
                    ws.cell(row=excel_row, column=col_idx, value=param_value)
            
            self.logger.info(f"数据填充完成，共 {len(data_list)} 行数据")
            
        except Exception as e:
            self.logger.error(f"数据填充失败: {e}")
    
    def _apply_styles(self, ws, data_count: int):
        """应用样式"""
        try:
            header_row = self.config.get_header_row()
            ab_row = self.config.get_ab_indicator_row()
            data_start_row = self.config.get_data_start_row()
            data_end_row = data_start_row + data_count - 1
            
            column_count = len(self.config.get_column_order())
            
            # 应用表头样式
            for col in range(1, column_count + 1):
                cell = ws.cell(row=header_row, column=col)
                cell.font = self.header_font
                cell.fill = self.header_fill
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = self.border
            
            # 应用A/B标识样式
            for col in range(1, column_count + 1):
                cell = ws.cell(row=ab_row, column=col)
                cell.font = self.header_font
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = self.border
                
                # A/B组分颜色
                if cell.value == "A":
                    cell.fill = self.ab_fill_a
                elif cell.value == "B":
                    cell.fill = self.ab_fill_b
                else:
                    cell.fill = self.header_fill
            
            # 应用数据样式
            for row in range(data_start_row, data_end_row + 1):
                for col in range(1, column_count + 1):
                    cell = ws.cell(row=row, column=col)
                    cell.font = self.data_font
                    cell.alignment = Alignment(horizontal='left', vertical='center')
                    cell.border = self.border
            
            self.logger.debug("样式应用完成")
            
        except Exception as e:
            self.logger.error(f"样式应用失败: {e}")
    
    def _adjust_column_widths(self, ws):
        """调整列宽"""
        try:
            column_order = self.config.get_column_order()
            
            for col_idx, col_letter in enumerate(column_order, 1):
                param_name = self.column_mapping[col_letter]
                
                # 计算合适的列宽
                if param_name == "序号":
                    width = 8
                elif param_name == "产品型号":
                    width = 20
                elif len(param_name) > 10:
                    width = min(len(param_name) + 5, 25)
                else:
                    width = 15
                
                # 设置列宽
                ws.column_dimensions[get_column_letter(col_idx)].width = width
            
            self.logger.debug("列宽调整完成")
            
        except Exception as e:
            self.logger.error(f"列宽调整失败: {e}")
    
    def generate_statistics_sheet(self, wb: Workbook, data_list: List[Dict[str, Any]]):
        """生成统计信息工作表"""
        try:
            # 创建统计工作表
            stats_ws = wb.create_sheet("统计信息")
            
            # 基本统计
            stats_ws['A1'] = "统计信息"
            stats_ws['A1'].font = Font(size=14, bold=True)
            
            stats_ws['A3'] = "总产品数量:"
            stats_ws['B3'] = len(data_list)
            
            # 文件类型统计
            file_types = {}
            for data in data_list:
                file_type = data.get('file_type', 'unknown')
                file_types[file_type] = file_types.get(file_type, 0) + 1
            
            row = 5
            stats_ws[f'A{row}'] = "文件类型分布:"
            row += 1
            
            for file_type, count in file_types.items():
                stats_ws[f'A{row}'] = f"  {file_type.upper()}:"
                stats_ws[f'B{row}'] = count
                row += 1
            
            # 参数覆盖率统计
            row += 1
            stats_ws[f'A{row}'] = "参数覆盖率:"
            row += 1
            
            all_params = set()
            param_counts = {}
            
            for data in data_list:
                parameters = data.get('parameters', {})
                all_params.update(parameters.keys())
                
                for param in parameters.keys():
                    param_counts[param] = param_counts.get(param, 0) + 1
            
            for param in sorted(all_params):
                coverage = param_counts[param] / len(data_list) * 100
                stats_ws[f'A{row}'] = f"  {param}:"
                stats_ws[f'B{row}'] = f"{coverage:.1f}%"
                row += 1
            
            self.logger.info("统计信息工作表生成完成")
            
        except Exception as e:
            self.logger.error(f"统计信息生成失败: {e}")
    
    def export_to_csv(self, data_list: List[Dict[str, Any]], output_file: str):
        """导出为CSV格式"""
        try:
            # 准备数据
            rows = []
            column_order = self.config.get_column_order()
            
            # 表头
            headers = [self.column_mapping[col] for col in column_order]
            rows.append(headers)
            
            # 数据行
            for idx, data in enumerate(data_list):
                row = []
                parameters = data.get('parameters', {})
                
                for col_letter in column_order:
                    param_name = self.column_mapping[col_letter]
                    
                    if param_name == "序号":
                        row.append(idx + 1)
                    elif param_name == "产品型号":
                        product_model = data.get('product_model', '') or data.get('product_name', '')
                        row.append(product_model)
                    else:
                        row.append(parameters.get(param_name, ""))
                
                rows.append(row)
            
            # 创建DataFrame并保存
            df = pd.DataFrame(rows[1:], columns=rows[0])
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"CSV文件导出完成: {output_file}")
            
        except Exception as e:
            self.logger.error(f"CSV导出失败: {e}")
    
    def get_generation_summary(self, data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """获取生成摘要信息"""
        try:
            # 统计信息
            total_products = len(data_list)
            
            # 文件类型统计
            file_types = {}
            for data in data_list:
                file_type = data.get('file_type', 'unknown')
                file_types[file_type] = file_types.get(file_type, 0) + 1
            
            # 参数统计
            all_params = set()
            param_counts = {}
            
            for data in data_list:
                parameters = data.get('parameters', {})
                all_params.update(parameters.keys())
                
                for param in parameters.keys():
                    param_counts[param] = param_counts.get(param, 0) + 1
            
            # 计算平均参数数量
            avg_params = sum(len(data.get('parameters', {})) for data in data_list) / total_products if total_products > 0 else 0
            
            return {
                'total_products': total_products,
                'file_types': file_types,
                'total_parameters': len(all_params),
                'avg_parameters_per_product': avg_params,
                'parameter_coverage': {
                    param: param_counts[param] / total_products * 100
                    for param in sorted(all_params)
                },
                'columns_generated': len(self.column_mapping)
            }
            
        except Exception as e:
            self.logger.error(f"摘要信息生成失败: {e}")
            return {}
