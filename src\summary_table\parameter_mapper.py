#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数映射器
处理参数名称匹配和A/B组分特殊逻辑
"""

import re
from typing import Dict, List, Tuple, Optional, Any
from rapidfuzz import fuzz, process
from ..utils import get_logger

logger = get_logger(__name__)


class ParameterMapper:
    """参数映射器"""
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # 获取配置
        self.param_mapping = config.get_param_mapping()
        self.column_mapping = config.get_column_mapping()
        
        # 构建反向映射（从列名到参数名）
        self.reverse_column_mapping = {v: k for k, v in self.column_mapping.items()}
        
        # A/B组分参数
        self.ab_parameters = config.get_ab_parameters()
        
        self.logger.info(f"参数映射器初始化完成，支持 {len(self.param_mapping)} 个参数")
    
    def map_parameters(self, extracted_params: Dict[str, str], product_name: str = "") -> Dict[str, str]:
        """
        将提取的参数映射到标准格式
        
        Args:
            extracted_params: 提取的原始参数
            product_name: 产品名称（用于A/B组分判断）
            
        Returns:
            Dict[str, str]: 映射后的参数
        """
        mapped_params = {}
        
        try:
            # 检测是否为A/B组分产品
            is_ab_product = self._detect_ab_product(extracted_params, product_name)
            
            for raw_param, raw_value in extracted_params.items():
                if not raw_param or not raw_value:
                    continue

                # 跳过已经由A/B组分提取器处理的参数
                if self._is_already_ab_processed(raw_param):
                    mapped_params[raw_param] = raw_value
                    self.logger.debug(f"保留A/B组分参数: {raw_param} = {raw_value}")
                    continue

                # 查找最佳匹配的标准参数名
                best_match = self._find_best_parameter_match(raw_param)

                if best_match:
                    standard_param, confidence = best_match

                    # 处理A/B组分逻辑（仅对非A/B组分提取器处理的参数）
                    # 但排除混合类参数，这些不应该被当作A/B组分处理
                    if (is_ab_product and
                        self._is_ab_parameter(standard_param) and
                        not self._is_mixed_parameter(raw_param)):
                        ab_params = self._handle_ab_component(standard_param, raw_value, raw_param)
                        mapped_params.update(ab_params)
                    else:
                        mapped_params[standard_param] = raw_value

                    self.logger.debug(f"参数映射: {raw_param} -> {standard_param} (置信度: {confidence:.2f})")
                else:
                    self.logger.debug(f"未找到匹配的参数: {raw_param}")
            
            self.logger.info(f"参数映射完成，映射了 {len(mapped_params)} 个参数")
            return mapped_params
            
        except Exception as e:
            self.logger.error(f"参数映射失败: {e}")
            return {}

    def _is_already_ab_processed(self, param_name: str) -> bool:
        """检查参数是否已经被A/B组分提取器处理过"""
        # A/B组分提取器处理的参数格式为：参数名(A) 或 参数名(B)
        return param_name.endswith('(A)') or param_name.endswith('(B)')

    def _detect_ab_product(self, extracted_params: Dict[str, str], product_name: str) -> bool:
        """检测是否为A/B组分产品"""
        # 检查产品名称
        ab_indicators = ["双组分", "双组份", "A/B", "A+B", "两组分", "两组份"]
        if any(indicator in product_name for indicator in ab_indicators):
            return True
        
        # 检查参数中是否有A/B组分相关信息
        ab_keywords = ["A组分", "B组分", "A剂", "B剂", "主剂", "固化剂", "混合比例"]
        for param_name in extracted_params.keys():
            if any(keyword in param_name for keyword in ab_keywords):
                return True
        
        # 检查是否有混合相关参数
        mixing_keywords = ["混合", "比例", "配比"]
        for param_name in extracted_params.keys():
            if any(keyword in param_name for keyword in mixing_keywords):
                return True
        
        return False
    
    def _find_best_parameter_match(self, raw_param: str) -> Optional[Tuple[str, float]]:
        """查找最佳匹配的标准参数名"""
        best_match = None
        best_score = 0
        
        # 清理参数名
        cleaned_param = self._clean_parameter_name(raw_param)
        
        for standard_param, aliases in self.param_mapping.items():
            for alias in aliases:
                # 计算相似度
                score = fuzz.ratio(cleaned_param, alias) / 100.0
                
                # 如果完全匹配，直接返回
                if score >= 0.95:
                    return (standard_param, score)
                
                # 记录最佳匹配
                if score > best_score and score >= 0.7:  # 最低阈值
                    best_score = score
                    best_match = standard_param
        
        if best_match:
            return (best_match, best_score)
        
        return None
    
    def _clean_parameter_name(self, param_name: str) -> str:
        """清理参数名称"""
        # 移除常见的修饰词
        cleaned = param_name.strip()
        
        # 移除单位信息
        cleaned = re.sub(r'\([^)]*\)', '', cleaned)
        cleaned = re.sub(r'（[^）]*）', '', cleaned)
        
        # 移除数字和特殊字符
        cleaned = re.sub(r'[0-9]+', '', cleaned)
        cleaned = re.sub(r'[^\u4e00-\u9fff\w]', '', cleaned)
        
        return cleaned.strip()
    
    def _is_ab_parameter(self, param_name: str) -> bool:
        """判断参数是否需要A/B组分处理"""
        return self.config.is_ab_parameter(param_name)

    def _is_mixed_parameter(self, raw_param: str) -> bool:
        """判断是否为混合类参数（不应该被当作A/B组分处理）"""
        mixed_keywords = ['混合', '固化后', '工艺', '应用']
        return any(keyword in raw_param for keyword in mixed_keywords)
    
    def _handle_ab_component(self, standard_param: str, value: str, raw_param: str) -> Dict[str, str]:
        """处理A/B组分参数"""
        result = {}
        
        try:
            # 检查原始参数名中是否明确指定了A或B组分
            if any(indicator in raw_param for indicator in ["A组分", "A剂", "主剂"]):
                result[f"{standard_param}(A)"] = value
            elif any(indicator in raw_param for indicator in ["B组分", "B剂", "固化剂"]):
                result[f"{standard_param}(B)"] = value
            else:
                # 如果没有明确指定，尝试解析值中的A/B信息
                ab_values = self._parse_ab_values(value)
                if ab_values:
                    if 'A' in ab_values:
                        result[f"{standard_param}(A)"] = ab_values['A']
                    if 'B' in ab_values:
                        result[f"{standard_param}(B)"] = ab_values['B']
                else:
                    # 默认同时填入A和B
                    result[f"{standard_param}(A)"] = value
                    result[f"{standard_param}(B)"] = value
            
            return result
            
        except Exception as e:
            self.logger.error(f"A/B组分处理失败: {e}")
            return {f"{standard_param}(A)": value, f"{standard_param}(B)": value}
    
    def _parse_ab_values(self, value: str) -> Optional[Dict[str, str]]:
        """解析值中的A/B组分信息"""
        try:
            # 常见的A/B分隔模式
            patterns = [
                r'A[组剂]?[分份]?[：:]\s*([^,，;；\n]+)[,，;；\s]*B[组剂]?[分份]?[：:]\s*([^,，;；\n]+)',
                r'主剂[：:]\s*([^,，;；\n]+)[,，;；\s]*固化剂[：:]\s*([^,，;；\n]+)',
                r'([^/]+)/([^/]+)',  # 简单的斜杠分隔
            ]
            
            for pattern in patterns:
                match = re.search(pattern, value)
                if match:
                    return {
                        'A': match.group(1).strip(),
                        'B': match.group(2).strip()
                    }
            
            return None
            
        except Exception as e:
            self.logger.debug(f"A/B值解析失败: {e}")
            return None
    
    def get_column_for_parameter(self, param_name: str) -> Optional[str]:
        """获取参数对应的Excel列"""
        return self.reverse_column_mapping.get(param_name)
    
    def get_all_standard_parameters(self) -> List[str]:
        """获取所有标准参数名"""
        return list(self.param_mapping.keys())
    
    def validate_mapped_parameters(self, mapped_params: Dict[str, str]) -> Dict[str, str]:
        """验证和清理映射后的参数"""
        validated = {}
        
        for param_name, value in mapped_params.items():
            # 检查参数名是否有效
            if param_name in self.param_mapping or param_name.replace("(A)", "").replace("(B)", "") in self.param_mapping:
                # 清理值
                cleaned_value = self._clean_parameter_value(value)
                if cleaned_value:
                    validated[param_name] = cleaned_value
            else:
                self.logger.debug(f"无效的参数名: {param_name}")
        
        return validated
    
    def _clean_parameter_value(self, value: str) -> str:
        """清理参数值"""
        if not value:
            return ""
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', value.strip())
        
        # 移除一些无意义的前缀
        prefixes_to_remove = ["参数:", "值:", "数值:", "Parameter:", "Value:"]
        for prefix in prefixes_to_remove:
            if cleaned.startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()
        
        return cleaned
    
    def get_mapping_statistics(self) -> Dict[str, Any]:
        """获取映射统计信息"""
        return {
            'total_parameters': len(self.param_mapping),
            'ab_parameters': len(self.ab_parameters),
            'column_count': len(self.column_mapping),
            'supported_aliases': sum(len(aliases) for aliases in self.param_mapping.values())
        }
