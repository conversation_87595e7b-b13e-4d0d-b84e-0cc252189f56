#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译数据库管理类
"""

import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Dict, Tuple
from contextlib import contextmanager

from .models import TranslationTerm, SmartPattern, TranslationLog, LearningRecord
from .models import TermType, TranslationSource

class TranslationDatabase:
    """翻译数据库管理类"""
    
    def __init__(self, db_path: str = "data/translation.db"):
        """初始化数据库"""
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        with self._get_connection() as conn:
            # 翻译术语表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS translation_terms (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chinese TEXT NOT NULL,
                    english TEXT NOT NULL,
                    term_type TEXT NOT NULL,
                    source TEXT NOT NULL,
                    confidence REAL DEFAULT 1.0,
                    usage_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 1.0,
                    context TEXT DEFAULT '',
                    notes TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    UNIQUE(chinese, term_type)
                )
            """)
            
            # 智能模式表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS smart_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern TEXT NOT NULL UNIQUE,
                    replacement TEXT NOT NULL,
                    description TEXT DEFAULT '',
                    priority INTEGER DEFAULT 100,
                    source TEXT NOT NULL,
                    usage_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 1.0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)
            
            # 翻译日志表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS translation_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_text TEXT NOT NULL,
                    translated_text TEXT NOT NULL,
                    translation_method TEXT NOT NULL,
                    term_id INTEGER,
                    pattern_id INTEGER,
                    quality_score REAL DEFAULT 0.0,
                    user_rating INTEGER,
                    feedback TEXT DEFAULT '',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (term_id) REFERENCES translation_terms (id),
                    FOREIGN KEY (pattern_id) REFERENCES smart_patterns (id)
                )
            """)
            
            # 学习记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_text TEXT NOT NULL,
                    suggested_translation TEXT NOT NULL,
                    current_translation TEXT DEFAULT '',
                    improvement_type TEXT NOT NULL,
                    confidence REAL DEFAULT 0.0,
                    status TEXT DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reviewed_at TIMESTAMP,
                    reviewer TEXT DEFAULT ''
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_terms_chinese ON translation_terms(chinese)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_terms_type ON translation_terms(term_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_patterns_priority ON smart_patterns(priority)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_method ON translation_logs(translation_method)")
            
            conn.commit()
            self.logger.info("数据库初始化完成")
    
    @contextmanager
    def _get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path, timeout=30.0)  # 增加超时时间
        conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
        conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式，提高并发性
        try:
            yield conn
        finally:
            conn.close()
    
    def add_term(self, term: TranslationTerm) -> int:
        """添加翻译术语"""
        with self._get_connection() as conn:
            try:
                cursor = conn.execute("""
                    INSERT INTO translation_terms 
                    (chinese, english, term_type, source, confidence, context, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    term.chinese, term.english, term.term_type.value, 
                    term.source.value, term.confidence, term.context, term.notes
                ))
                conn.commit()
                self.logger.info(f"添加术语: {term.chinese} -> {term.english}")
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                # 处理重复条目
                self.logger.warning(f"术语已存在，尝试更新: {term.chinese}")
                return self.update_term(term.chinese, term)
    
    def update_term(self, chinese: str, term: TranslationTerm) -> int:
        """更新翻译术语"""
        with self._get_connection() as conn:
            cursor = conn.execute("""
                UPDATE translation_terms 
                SET english = ?, confidence = ?, context = ?, notes = ?, 
                    updated_at = CURRENT_TIMESTAMP, usage_count = usage_count + 1
                WHERE chinese = ? AND term_type = ?
            """, (
                term.english, term.confidence, term.context, term.notes,
                chinese, term.term_type.value
            ))
            conn.commit()
            if cursor.rowcount > 0:
                self.logger.info(f"更新术语: {chinese} -> {term.english}")
            return cursor.rowcount
    
    def get_term(self, chinese: str, term_type: Optional[TermType] = None) -> Optional[TranslationTerm]:
        """获取翻译术语"""
        with self._get_connection() as conn:
            if term_type:
                row = conn.execute("""
                    SELECT * FROM translation_terms 
                    WHERE chinese = ? AND term_type = ? AND is_active = 1
                    ORDER BY confidence DESC, usage_count DESC
                    LIMIT 1
                """, (chinese, term_type.value)).fetchone()
            else:
                row = conn.execute("""
                    SELECT * FROM translation_terms 
                    WHERE chinese = ? AND is_active = 1
                    ORDER BY confidence DESC, usage_count DESC
                    LIMIT 1
                """, (chinese,)).fetchone()
            
            if row:
                return self._row_to_term(row)
            return None
    
    def get_all_terms(self, term_type: Optional[TermType] = None) -> Dict[str, str]:
        """获取所有翻译术语的字典"""
        with self._get_connection() as conn:
            if term_type:
                rows = conn.execute("""
                    SELECT chinese, english FROM translation_terms 
                    WHERE term_type = ? AND is_active = 1
                    ORDER BY confidence DESC, usage_count DESC
                """, (term_type.value,)).fetchall()
            else:
                rows = conn.execute("""
                    SELECT chinese, english FROM translation_terms 
                    WHERE is_active = 1
                    ORDER BY confidence DESC, usage_count DESC
                """).fetchall()
            
            # 处理重复的中文术语，保留置信度和使用次数最高的
            result = {}
            for row in rows:
                if row['chinese'] not in result:
                    result[row['chinese']] = row['english']
            
            return result
    
    def _row_to_term(self, row) -> TranslationTerm:
        """将数据库行转换为TranslationTerm对象"""
        return TranslationTerm(
            id=row['id'],
            chinese=row['chinese'],
            english=row['english'],
            term_type=TermType(row['term_type']),
            source=TranslationSource(row['source']),
            confidence=row['confidence'],
            usage_count=row['usage_count'],
            success_rate=row['success_rate'],
            context=row['context'],
            notes=row['notes'],
            created_at=datetime.fromisoformat(row['created_at']) if row['created_at'] else None,
            updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None,
            is_active=bool(row['is_active'])
        )

    def add_smart_pattern(self, pattern: SmartPattern) -> int:
        """添加智能模式"""
        with self._get_connection() as conn:
            try:
                cursor = conn.execute("""
                    INSERT INTO smart_patterns
                    (pattern, replacement, description, priority, source)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    pattern.pattern, pattern.replacement, pattern.description,
                    pattern.priority, pattern.source.value
                ))
                conn.commit()
                self.logger.info(f"添加智能模式: {pattern.description}")
                return cursor.lastrowid
            except sqlite3.IntegrityError:
                self.logger.warning(f"智能模式已存在: {pattern.pattern}")
                return 0

    def get_smart_patterns(self) -> List[Tuple[str, str]]:
        """获取所有智能模式"""
        with self._get_connection() as conn:
            rows = conn.execute("""
                SELECT pattern, replacement FROM smart_patterns
                WHERE is_active = 1
                ORDER BY priority ASC, usage_count DESC
            """).fetchall()

            return [(row['pattern'], row['replacement']) for row in rows]

    def log_translation(self, log: TranslationLog) -> int:
        """记录翻译日志"""
        with self._get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO translation_logs
                (original_text, translated_text, translation_method, term_id,
                 pattern_id, quality_score, user_rating, feedback)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                log.original_text, log.translated_text, log.translation_method,
                log.term_id, log.pattern_id, log.quality_score,
                log.user_rating, log.feedback
            ))
            conn.commit()
            return cursor.lastrowid

    def add_learning_record(self, record: LearningRecord) -> int:
        """添加学习记录"""
        with self._get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO learning_records
                (original_text, suggested_translation, current_translation,
                 improvement_type, confidence)
                VALUES (?, ?, ?, ?, ?)
            """, (
                record.original_text, record.suggested_translation,
                record.current_translation, record.improvement_type, record.confidence
            ))
            conn.commit()
            self.logger.info(f"添加学习记录: {record.original_text}")
            return cursor.lastrowid

    def get_statistics(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        with self._get_connection() as conn:
            stats = {}

            # 术语统计
            stats['total_terms'] = conn.execute(
                "SELECT COUNT(*) FROM translation_terms WHERE is_active = 1"
            ).fetchone()[0]

            # 智能模式统计
            stats['total_patterns'] = conn.execute(
                "SELECT COUNT(*) FROM smart_patterns WHERE is_active = 1"
            ).fetchone()[0]

            # 翻译日志统计
            stats['total_translations'] = conn.execute(
                "SELECT COUNT(*) FROM translation_logs"
            ).fetchone()[0]

            # 学习记录统计
            stats['pending_learning'] = conn.execute(
                "SELECT COUNT(*) FROM learning_records WHERE status = 'pending'"
            ).fetchone()[0]

            return stats

    def cleanup_duplicates(self) -> int:
        """清理重复数据"""
        with self._get_connection() as conn:
            # 清理重复的术语，保留最新的
            cursor = conn.execute("""
                DELETE FROM translation_terms
                WHERE id NOT IN (
                    SELECT MAX(id) FROM translation_terms
                    GROUP BY chinese, term_type
                )
            """)
            removed_count = cursor.rowcount
            conn.commit()

            if removed_count > 0:
                self.logger.info(f"清理了 {removed_count} 个重复术语")

            return removed_count
