2025-07-29 16:31:30,899 - __main__ - ERROR - logger.py:84 - 程序执行出错: 'NoneType' object has no attribute 'write'
2025-07-29 16:31:30,900 - __main__ - ERROR - logger.py:84 - Traceback (most recent call last):
  File "main.py", line 215, in main
  File "main.py", line 56, in process_folder
  File "src\utils\progress.py", line 152, in create_progress_tracker
    return ProgressTracker(total_files, description)
  File "src\utils\progress.py", line 27, in __init__
    self.pbar = tqdm(
                ~~~~^
        total=total_files,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "tqdm\std.py", line 1098, in __init__
  File "tqdm\std.py", line 1347, in refresh
  File "tqdm\std.py", line 1495, in display
  File "tqdm\std.py", line 459, in print_status
  File "tqdm\std.py", line 452, in fp_write
  File "tqdm\utils.py", line 140, in __getattr__
AttributeError: 'NoneType' object has no attribute 'write'

