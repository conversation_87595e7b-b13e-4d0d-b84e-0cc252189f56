#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
建议引擎
为术语翻译提供多个选项建议
"""

import re
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class TranslationSuggestion:
    """翻译建议"""
    english: str
    confidence: float
    source: str  # 来源：dict/pattern/ai/manual
    explanation: str

class SuggestionEngine:
    """建议引擎"""

    def __init__(self, enable_reverse_validation: bool = True):
        self.enable_reverse_validation = enable_reverse_validation
        self.reverse_validator = None

        # 初始化数据库连接以检查已有翻译
        try:
            from ..database import TranslationDatabase
            self.db = TranslationDatabase()
            self.existing_terms = self.db.get_all_terms()
        except Exception as e:
            print(f"⚠️  数据库连接失败: {e}")
            self.db = None
            self.existing_terms = {}

        if enable_reverse_validation:
            try:
                from .reverse_validator import ReverseTranslationValidator
                self.reverse_validator = ReverseTranslationValidator()
            except Exception as e:
                print(f"⚠️  反向验证器初始化失败: {e}")
                self.enable_reverse_validation = False
        # 常见翻译模式
        self.translation_patterns = {
            # 性能类
            r'(.+)性能$': [
                (r'\1 Performance', 0.9, '通用性能翻译'),
                (r'\1 Properties', 0.8, '属性类翻译'),
                (r'\1 Characteristics', 0.7, '特性类翻译')
            ],
            
            # 条件类
            r'(.+)条件$': [
                (r'\1 Conditions', 0.9, '条件类翻译'),
                (r'\1 Requirements', 0.7, '要求类翻译')
            ],
            
            # 工艺类
            r'(.+)工艺$': [
                (r'\1 Process', 0.9, '工艺流程翻译'),
                (r'\1 Technology', 0.7, '技术类翻译'),
                (r'\1 Procedure', 0.6, '程序类翻译')
            ],
            
            # 材料类
            r'(.+)材料$': [
                (r'\1 Material', 0.9, '材料类翻译'),
                (r'\1 Materials', 0.8, '复数材料翻译')
            ],
            
            # 设备类
            r'(.+)设备$': [
                (r'\1 Equipment', 0.9, '设备类翻译'),
                (r'\1 Device', 0.7, '装置类翻译'),
                (r'\1 Apparatus', 0.6, '仪器类翻译')
            ],
        }
        
        # 常见词汇对应（改进版）
        self.common_mappings = {
            '常规': ['General', 'Regular', 'Standard'],
            '固化后': ['Cured', 'After Curing', 'Post-Cure'],
            '储存': ['Storage', 'Store', 'Storing'],
            '运输': ['Transportation', 'Transport', 'Shipping'],
            '贮存': ['Storage', 'Store', 'Preservation'],
            '重要': ['Important', 'Critical', 'Key'],
            '申明': ['Statement', 'Declaration', 'Notice'],
            '说明': ['Description', 'Instruction', 'Note'],
            '室温': ['Room Temperature', 'Ambient Temperature'],
            '双组份': ['Two-component', 'Dual Component', 'Two-part'],
            '单组份': ['One-component', 'Single Component', 'One-part'],
            '电绝缘': ['Electrical Insulation', 'Electric Insulation'],
            '绝缘': ['Insulation', 'Insulating'],
            '测试': ['Test', 'Testing'],
            '标准': ['Standard', 'Standards'],
        }

        # 复合词汇的完整翻译（优先级更高）
        self.compound_translations = {
            '贮存运输': 'Storage and Transportation',
            '储存运输': 'Storage and Transportation',
            '室温固化': 'Room Temperature Curing',
            '电绝缘性能': 'Electrical Insulation Performance',
            '固化后性能': 'Cured Properties',
            '测试标准': 'Test Standards',
            '包装规格': 'Package Specifications',
            '储存条件': 'Storage Conditions',
            '储存期限': 'Storage Period',
            '使用环境': 'Usage Environment',
            '保质期': 'Shelf Life',
            '双组份': 'Two-component',
            '单组份': 'One-component',
            '聚氨酯密封胶': 'Polyurethane Sealant',
            '阻燃型': 'Flame Retardant',
            '功能性填充材料': 'Functional Filler Materials',
            '质量验证': 'Quality Verification',
            '典型数据': 'Typical Data',
            '完全固化': 'Complete Curing',
            '室温密封': 'Room Temperature Sealing',
        }

        # 常见短语翻译
        self.phrase_translations = {
            '自生产之日起': 'from the date of production',
            '如密封条未损坏': 'if the seal is not damaged',
            '应尽快使用完毕': 'should be used up as soon as possible',
            '立即密封保存': 'seal and store immediately',
            '避免受潮': 'avoid moisture',
            '按一般化学品贮运': 'store and transport as general chemicals',
            '非危险品': 'non-hazardous products',
            '仅供客户使用时参考': 'for customer reference only',
        }
    
    def generate_suggestions(self, chinese_term: str, context: str = "") -> List[TranslationSuggestion]:
        """生成翻译建议"""
        suggestions = []

        # 0. 首先检查数据库中是否已有翻译
        if self.existing_terms and chinese_term in self.existing_terms:
            existing_translation = self.existing_terms[chinese_term]
            # 如果已有高质量翻译，不生成建议
            if len(existing_translation) > 10:  # 假设长翻译质量更好
                return []  # 返回空建议列表
            else:
                # 如果现有翻译较短，可能需要改进，但置信度较低
                suggestion = TranslationSuggestion(
                    english=existing_translation,
                    confidence=0.5,
                    source='existing_db',
                    explanation='数据库中已有翻译，但可能需要改进'
                )
                suggestions.append(suggestion)

        # 1. 优先检查完整复合词翻译
        if chinese_term in self.compound_translations:
            suggestion = TranslationSuggestion(
                english=self.compound_translations[chinese_term],
                confidence=0.98,
                source='compound_dict',
                explanation='完整词汇翻译（推荐）'
            )
            suggestions.append(suggestion)

        # 1. 检查短语翻译
        phrase_suggestions = self._get_phrase_suggestions(chinese_term)
        suggestions.extend(phrase_suggestions)

        # 2. 智能组合翻译（只有在没有完整翻译时）
        if not suggestions:
            smart_suggestions = self._get_smart_combination_suggestions(chinese_term)
            suggestions.extend(smart_suggestions)

        # 3. 基于模式的建议（降级处理）
        if len(suggestions) < 2:
            pattern_suggestions = self._get_pattern_suggestions(chinese_term)
            suggestions.extend(pattern_suggestions)

        # 去重并排序
        unique_suggestions = self._deduplicate_suggestions(suggestions)
        # 调整置信度并过滤低质量建议
        adjusted_suggestions = self._adjust_confidence(unique_suggestions, chinese_term)

        # 反向验证（如果启用）
        if self.enable_reverse_validation and self.reverse_validator:
            validated_suggestions = self._apply_reverse_validation(adjusted_suggestions, chinese_term)
        else:
            validated_suggestions = adjusted_suggestions

        # 只返回高质量建议
        high_quality = [s for s in validated_suggestions if s.confidence > 0.6]
        return sorted(high_quality, key=lambda x: x.confidence, reverse=True)[:3]

    def _get_phrase_suggestions(self, term: str) -> List[TranslationSuggestion]:
        """获取短语翻译建议"""
        suggestions = []

        for phrase, translation in self.phrase_translations.items():
            if phrase in term:
                # 尝试替换整个短语
                if phrase == term:
                    suggestion = TranslationSuggestion(
                        english=translation,
                        confidence=0.95,
                        source='phrase_dict',
                        explanation='常用短语翻译'
                    )
                    suggestions.append(suggestion)

        return suggestions

    def _get_smart_combination_suggestions(self, term: str) -> List[TranslationSuggestion]:
        """智能组合翻译建议"""
        suggestions = []

        # 尝试分解并组合翻译
        best_combination = self._smart_decompose_and_translate(term)
        if best_combination and not self._contains_chinese_english_mix(best_combination):
            suggestion = TranslationSuggestion(
                english=best_combination,
                confidence=0.85,
                source='smart_combination',
                explanation='智能组合翻译'
            )
            suggestions.append(suggestion)

        return suggestions

    def _smart_decompose_and_translate(self, term: str) -> str:
        """智能分解并翻译"""
        # 按优先级尝试分解
        parts = []
        remaining = term

        # 首先匹配复合词
        for compound, translation in self.compound_translations.items():
            if compound in remaining:
                parts.append(translation)
                remaining = remaining.replace(compound, '', 1)

        # 然后匹配常见词汇
        for chinese, english_list in self.common_mappings.items():
            if chinese in remaining:
                parts.append(english_list[0])
                remaining = remaining.replace(chinese, '', 1)

        # 如果还有剩余，尝试模式匹配
        if remaining.strip():
            for pattern, replacements in self.translation_patterns.items():
                import re
                match = re.match(pattern, remaining.strip())
                if match:
                    prefix = match.group(1) if match.groups() else ""
                    if prefix in self.common_mappings:
                        parts.append(self.common_mappings[prefix][0] + " " + replacements[0][0].split()[-1])
                    break

        if parts:
            return ' '.join(parts).strip()
        return ""

    def _contains_chinese_english_mix(self, text: str) -> bool:
        """检查是否包含中英混杂"""
        import re
        return bool(re.search(r'[\u4e00-\u9fa5]', text))

    def _get_pattern_suggestions(self, term: str) -> List[TranslationSuggestion]:
        """基于模式的建议"""
        suggestions = []
        
        for pattern, replacements in self.translation_patterns.items():
            match = re.match(pattern, term)
            if match:
                prefix = match.group(1) if match.groups() else ""
                
                for replacement_pattern, confidence, explanation in replacements:
                    try:
                        english = replacement_pattern.replace(r'\1', self._translate_prefix(prefix))
                        suggestion = TranslationSuggestion(
                            english=english,
                            confidence=confidence,
                            source='pattern',
                            explanation=f'{explanation} (模式匹配)'
                        )
                        suggestions.append(suggestion)
                    except:
                        continue
        
        return suggestions
    
    def _get_mapping_suggestions(self, term: str) -> List[TranslationSuggestion]:
        """基于常见映射的建议"""
        suggestions = []
        
        for chinese_part, english_options in self.common_mappings.items():
            if chinese_part in term:
                for i, english_option in enumerate(english_options):
                    confidence = 0.9 - i * 0.1  # 第一个选项置信度最高
                    
                    # 如果是完全匹配
                    if chinese_part == term:
                        english = english_option
                    else:
                        # 部分匹配，需要组合
                        english = term.replace(chinese_part, english_option)
                        english = self._clean_mixed_text(english)
                    
                    suggestion = TranslationSuggestion(
                        english=english,
                        confidence=confidence,
                        source='mapping',
                        explanation=f'常见词汇映射 ({chinese_part} → {english_option})'
                    )
                    suggestions.append(suggestion)
        
        return suggestions
    
    def _get_compound_suggestions(self, term: str) -> List[TranslationSuggestion]:
        """基于组合词的建议"""
        suggestions = []
        
        # 分解复合词
        parts = self._decompose_compound(term)
        if len(parts) > 1:
            translated_parts = []
            for part in parts:
                if part in self.common_mappings:
                    translated_parts.append(self.common_mappings[part][0])
                else:
                    translated_parts.append(part)  # 保持原文
            
            # 组合翻译
            english = ' '.join(translated_parts)
            english = self._clean_mixed_text(english)
            
            suggestion = TranslationSuggestion(
                english=english,
                confidence=0.7,
                source='compound',
                explanation='复合词分解翻译'
            )
            suggestions.append(suggestion)
        
        return suggestions
    
    def _get_context_suggestions(self, term: str, context: str) -> List[TranslationSuggestion]:
        """基于上下文的建议"""
        suggestions = []
        
        if not context:
            return suggestions
        
        # 基于上下文的特殊处理
        context_rules = {
            '技术参数表': {
                '性能': 'Properties',
                '条件': 'Conditions',
                '标准': 'Standard'
            },
            '工艺': {
                '性能': 'Performance',
                '条件': 'Conditions',
                '过程': 'Process'
            }
        }
        
        for context_key, term_mappings in context_rules.items():
            if context_key in context:
                for term_part, preferred_translation in term_mappings.items():
                    if term_part in term:
                        english = term.replace(term_part, preferred_translation)
                        english = self._clean_mixed_text(english)
                        
                        suggestion = TranslationSuggestion(
                            english=english,
                            confidence=0.8,
                            source='context',
                            explanation=f'基于上下文 "{context_key}" 的翻译'
                        )
                        suggestions.append(suggestion)
        
        return suggestions
    
    def _translate_prefix(self, prefix: str) -> str:
        """翻译前缀"""
        if prefix in self.common_mappings:
            return self.common_mappings[prefix][0]
        return prefix  # 如果找不到映射，保持原文
    
    def _decompose_compound(self, term: str) -> List[str]:
        """分解复合词"""
        # 简单的分解策略
        parts = []
        
        # 基于常见词汇边界分解
        for chinese_part in self.common_mappings.keys():
            if chinese_part in term and len(chinese_part) >= 2:
                parts.append(chinese_part)
                term = term.replace(chinese_part, '|')
        
        # 处理剩余部分
        remaining_parts = [p for p in term.split('|') if p.strip()]
        parts.extend(remaining_parts)
        
        return [p for p in parts if p.strip()]
    
    def _clean_mixed_text(self, text: str) -> str:
        """清理混合文本"""
        # 移除多余的空格
        text = re.sub(r'\s+', ' ', text)
        
        # 处理中英文混合的情况
        # 如果还有中文，尝试进一步翻译
        chinese_parts = re.findall(r'[\u4e00-\u9fa5]+', text)
        for chinese_part in chinese_parts:
            if chinese_part in self.common_mappings:
                text = text.replace(chinese_part, self.common_mappings[chinese_part][0])
        
        return text.strip()

    def _adjust_confidence(self, suggestions: List[TranslationSuggestion], chinese_term: str) -> List[TranslationSuggestion]:
        """调整建议的置信度"""
        adjusted = []

        for suggestion in suggestions:
            new_confidence = suggestion.confidence

            # 质量检查
            quality_score = self._evaluate_translation_quality(chinese_term, suggestion.english)

            # 根据质量调整置信度
            if quality_score < 0.5:
                new_confidence *= 0.6  # 大幅降低
            elif quality_score < 0.7:
                new_confidence *= 0.8  # 适度降低
            elif quality_score > 0.9:
                new_confidence = min(0.98, new_confidence * 1.1)  # 适度提高

            # 根据来源调整置信度
            if suggestion.source == 'compound_dict':
                new_confidence = max(0.95, new_confidence)  # 复合词典最高
            elif suggestion.source == 'mapping' and quality_score > 0.8:
                new_confidence = max(0.85, new_confidence)  # 高质量映射
            elif suggestion.source == 'pattern' and quality_score < 0.7:
                new_confidence *= 0.7  # 低质量模式匹配

            adjusted_suggestion = TranslationSuggestion(
                english=suggestion.english,
                confidence=new_confidence,
                source=suggestion.source,
                explanation=suggestion.explanation
            )
            adjusted.append(adjusted_suggestion)

        return adjusted

    def _evaluate_translation_quality(self, chinese: str, english: str) -> float:
        """评估翻译质量"""
        score = 1.0

        # 检查是否有中英混杂
        import re
        if re.search(r'[\u4e00-\u9fa5]', english):
            score -= 0.4  # 包含中文，严重扣分

        # 检查是否有明显的拼接问题
        if re.search(r'[a-zA-Z][\u4e00-\u9fa5]|[\u4e00-\u9fa5][a-zA-Z]', english):
            score -= 0.3  # 中英直接拼接

        # 检查空格问题
        if re.search(r'[A-Z][a-z]+[A-Z]', english) and ' ' not in english:
            score -= 0.2  # 缺少空格的复合词

        # 检查长度合理性
        if len(english) > len(chinese) * 3:
            score -= 0.2  # 过长
        elif len(english) < len(chinese) * 0.5:
            score -= 0.1  # 过短

        # 检查是否包含常见的错误翻译
        error_patterns = [
            r'double-assembly',  # 应该是 two-component
            r'seating devices',  # 应该是 toilets 或 smart toilets
            r'gas point firearms',  # 应该是 gas igniters
        ]

        for pattern in error_patterns:
            if re.search(pattern, english, re.IGNORECASE):
                score -= 0.3

        return max(0.0, score)

    def _apply_reverse_validation(self, suggestions: List[TranslationSuggestion], chinese_term: str) -> List[TranslationSuggestion]:
        """应用反向翻译验证"""
        validated_suggestions = []

        for suggestion in suggestions:
            try:
                # 进行反向验证
                similarity_score, validation_note = self.reverse_validator.validate_translation(
                    chinese_term, suggestion.english
                )

                # 根据验证结果调整置信度和说明
                if similarity_score > 0.8:
                    # 高质量验证通过
                    new_confidence = min(0.98, suggestion.confidence * 1.2)
                    new_explanation = f"{suggestion.explanation} ✅ 反向验证通过"
                elif similarity_score > 0.6:
                    # 中等质量
                    new_confidence = suggestion.confidence * 1.0
                    new_explanation = f"{suggestion.explanation} ⚠️ 反向验证部分通过"
                else:
                    # 低质量，降级
                    new_confidence = suggestion.confidence * 0.7
                    new_explanation = f"{suggestion.explanation} ❌ 反向验证未通过"

                validated_suggestion = TranslationSuggestion(
                    english=suggestion.english,
                    confidence=new_confidence,
                    source=suggestion.source,
                    explanation=new_explanation
                )

                validated_suggestions.append(validated_suggestion)

            except Exception as e:
                # 验证失败，保持原建议但降低置信度
                fallback_suggestion = TranslationSuggestion(
                    english=suggestion.english,
                    confidence=suggestion.confidence * 0.8,
                    source=suggestion.source,
                    explanation=f"{suggestion.explanation} (验证失败)"
                )
                validated_suggestions.append(fallback_suggestion)

        return validated_suggestions

    def _deduplicate_suggestions(self, suggestions: List[TranslationSuggestion]) -> List[TranslationSuggestion]:
        """去重建议"""
        seen = set()
        unique_suggestions = []
        
        for suggestion in suggestions:
            key = suggestion.english.lower()
            if key not in seen:
                seen.add(key)
                unique_suggestions.append(suggestion)
        
        return unique_suggestions
