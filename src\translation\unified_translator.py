#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一翻译引擎
基于第一性原则重构的翻译系统，遵循单一职责和系统性思维
"""

import re
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
from ..utils import get_logger

logger = get_logger(__name__)


class TranslationStage(Enum):
    """翻译阶段枚举"""
    TEXT_EXTRACTION = "text_extraction"
    PUNCTUATION_PROCESSING = "punctuation_processing"
    TEXT_SEGMENTATION = "text_segmentation"
    DICTIONARY_MATCHING = "dictionary_matching"
    QUALITY_CHECK_1 = "quality_check_1"
    HELSINKI_TRANSLATION = "helsinki_translation"
    QUALITY_CHECK_2 = "quality_check_2"
    TENCENT_TRANSLATION = "tencent_translation"
    QUALITY_CHECK_3 = "quality_check_3"
    FINE_SEGMENTATION_RETRY = "fine_segmentation_retry"
    LEARNING_SUGGESTIONS = "learning_suggestions"
    OUTPUT_FORMATTING = "output_formatting"


class TranslationResult(Enum):
    """翻译结果状态"""
    SUCCESS = "success"
    PARTIAL = "partial"
    FAILED = "failed"
    NEEDS_MANUAL = "needs_manual"


@dataclass
class TranslationSegment:
    """翻译片段数据结构"""
    original_text: str
    translated_text: str = ""
    stage_completed: TranslationStage = TranslationStage.TEXT_EXTRACTION
    result_status: TranslationResult = TranslationResult.FAILED
    confidence_score: float = 0.0
    chinese_remaining: float = 0.0  # 剩余中文比例
    translation_method: str = ""
    error_message: str = ""


@dataclass
class TranslationContext:
    """翻译上下文"""
    segments: List[TranslationSegment]
    original_format: Dict[str, Any]  # 原始格式信息
    learning_suggestions: List[Dict[str, str]]
    quality_metrics: Dict[str, float]
    processing_log: List[str]


class UnifiedTranslator:
    """
    统一翻译引擎
    
    实现完整的翻译流程：
    1. 文本获取和预处理
    2. 中文标点符号处理
    3. 智能分段
    4. SQLite词典匹配
    5. 质量检查
    6. Helsinki-NLP翻译
    7. 质量检查
    8. 腾讯翻译API
    9. 质量检查
    10. 细分重试机制
    11. 学习建议生成
    12. 格式化输出
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化统一翻译器"""
        self.config = config or {}
        self.logger = get_logger(__name__)
        
        # 初始化各个组件
        self._init_components()
        
        # 翻译流程配置
        self.max_retry_attempts = self.config.get('max_retry_attempts', 3)
        self.fine_segmentation_threshold = self.config.get('fine_segmentation_threshold', 0.3)
        
        self.logger.info("统一翻译引擎初始化完成")
    
    def _init_components(self):
        """初始化翻译组件"""
        try:
            # 标点处理器
            from .punctuation_processor import PunctuationProcessor
            self.punctuation_processor = PunctuationProcessor()

            # 智能分段器
            from .smart_segmenter import SmartSegmenter
            self.segmenter = SmartSegmenter()

            # 质量检查器
            from .quality_checker import QualityChecker
            self.quality_checker = QualityChecker()

            # 技术词典
            from .technical_dictionary import get_technical_dictionary
            self.tech_dict = get_technical_dictionary()
            
            # Helsinki翻译器
            try:
                from .translator import HelsinkiTranslator
                self.helsinki_translator = HelsinkiTranslator()
                self.helsinki_available = True
            except Exception as e:
                self.logger.warning(f"Helsinki翻译器不可用: {e}")
                self.helsinki_translator = None
                self.helsinki_available = False
            
            # 腾讯翻译器
            try:
                from .translator import TencentTranslator
                self.tencent_translator = TencentTranslator()
                self.tencent_available = True
            except Exception as e:
                self.logger.warning(f"腾讯翻译器不可用: {e}")
                self.tencent_translator = None
                self.tencent_available = False
            
            # 学习建议系统
            from .learning_advisor import LearningAdvisor
            self.learning_advisor = LearningAdvisor()
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
            raise
    
    def translate_text(self, text: str, preserve_format: bool = True) -> TranslationContext:
        """
        翻译单个文本
        
        Args:
            text: 要翻译的文本
            preserve_format: 是否保留原始格式
            
        Returns:
            TranslationContext: 翻译结果上下文
        """
        if not text or not text.strip():
            return self._create_empty_context()
        
        # 创建翻译上下文
        context = TranslationContext(
            segments=[],
            original_format={'text': text, 'preserve_format': preserve_format},
            learning_suggestions=[],
            quality_metrics={},
            processing_log=[]
        )
        
        try:
            # 执行翻译流程
            context = self._execute_translation_pipeline(text, context)
            
            # 生成质量指标
            context.quality_metrics = self._calculate_quality_metrics(context)
            
            # 生成学习建议
            learning_report = self.learning_advisor.analyze_translation_session(context)
            context.learning_suggestions = [
                {
                    'type': item.item_type,
                    'chinese': item.chinese,
                    'english': item.english,
                    'confidence': item.confidence,
                    'priority': item.priority,
                    'suggestions': item.suggestions
                }
                for item in learning_report.learning_items
            ]
            
            self.logger.info(f"翻译完成，处理了 {len(context.segments)} 个片段")
            
        except Exception as e:
            self.logger.error(f"翻译过程出错: {e}")
            context.processing_log.append(f"ERROR: {str(e)}")
        
        return context
    
    def _execute_translation_pipeline(self, text: str, context: TranslationContext) -> TranslationContext:
        """执行翻译流水线"""
        
        # 阶段1: 标点符号处理
        processed_text = self._stage_punctuation_processing(text, context)
        
        # 阶段2: 文本分段
        segments = self._stage_text_segmentation(processed_text, context)
        
        # 阶段3: 词典匹配
        segments = self._stage_dictionary_matching(segments, context)
        
        # 阶段4: Helsinki翻译
        segments = self._stage_helsinki_translation(segments, context)
        
        # 阶段5: 腾讯翻译
        segments = self._stage_tencent_translation(segments, context)
        
        # 阶段6: 细分重试
        segments = self._stage_fine_segmentation_retry(segments, context)
        
        context.segments = segments
        return context
    
    def _stage_punctuation_processing(self, text: str, context: TranslationContext) -> str:
        """阶段1: 中文标点符号处理"""
        context.processing_log.append("开始标点符号处理")
        
        try:
            processed_text = self.punctuation_processor.convert_punctuation(text)
            context.processing_log.append(f"标点处理完成，原长度: {len(text)}, 处理后: {len(processed_text)}")
            return processed_text
        except Exception as e:
            context.processing_log.append(f"标点处理失败: {e}")
            return text
    
    def _stage_text_segmentation(self, text: str, context: TranslationContext) -> List[TranslationSegment]:
        """阶段2: 文本分段"""
        context.processing_log.append("开始文本分段")

        try:
            # 使用智能分段器进行分割
            segments_text = self.segmenter.segment_text(text, max_length=20)

            # 创建翻译片段对象
            segments = []
            for segment_text in segments_text:
                if segment_text.strip():
                    segment = TranslationSegment(
                        original_text=segment_text.strip(),
                        stage_completed=TranslationStage.TEXT_SEGMENTATION
                    )
                    segments.append(segment)

            # 评估分段质量
            quality = self.segmenter.get_segmentation_quality(segments_text)
            context.processing_log.append(
                f"文本分段完成，生成 {len(segments)} 个片段，质量分数: {quality['quality_score']:.2f}"
            )

            return segments

        except Exception as e:
            context.processing_log.append(f"文本分段失败: {e}")
            # 创建单个片段作为备选
            return [TranslationSegment(original_text=text)]
    
    def _stage_dictionary_matching(self, segments: List[TranslationSegment], context: TranslationContext) -> List[TranslationSegment]:
        """阶段3: SQLite词典匹配"""
        context.processing_log.append("开始词典匹配")
        
        matched_count = 0
        for segment in segments:
            if segment.result_status != TranslationResult.FAILED:
                continue
                
            try:
                # 检查是否为技术术语
                if self.tech_dict.is_technical_term(segment.original_text):
                    translation = self.tech_dict.translate_term(segment.original_text)
                    if translation and translation != segment.original_text:
                        segment.translated_text = translation
                        segment.stage_completed = TranslationStage.DICTIONARY_MATCHING
                        segment.result_status = TranslationResult.SUCCESS
                        segment.confidence_score = 0.95
                        segment.translation_method = "技术词典"
                        matched_count += 1
                        
            except Exception as e:
                segment.error_message = f"词典匹配错误: {e}"
        
        context.processing_log.append(f"词典匹配完成，匹配 {matched_count} 个片段")
        return segments

    def _stage_helsinki_translation(self, segments: List[TranslationSegment], context: TranslationContext) -> List[TranslationSegment]:
        """阶段4: Helsinki-NLP翻译"""
        if not self.helsinki_available:
            context.processing_log.append("Helsinki翻译器不可用，跳过")
            return segments

        context.processing_log.append("开始Helsinki翻译")

        # 收集需要翻译的片段
        pending_segments = [s for s in segments if s.result_status == TranslationResult.FAILED]
        if not pending_segments:
            context.processing_log.append("无需要Helsinki翻译的片段")
            return segments

        translated_count = 0
        for segment in pending_segments:
            try:
                translation = self.helsinki_translator.translate(segment.original_text)

                # 质量检查
                quality_result = self.quality_checker.check_translation_quality(segment.original_text, translation)
                if quality_result.is_valid:
                    segment.translated_text = translation
                    segment.stage_completed = TranslationStage.HELSINKI_TRANSLATION
                    segment.result_status = TranslationResult.SUCCESS
                    segment.confidence_score = quality_result.confidence_score
                    segment.chinese_remaining = quality_result.chinese_remaining_ratio
                    segment.translation_method = "Helsinki-NLP"
                    translated_count += 1
                else:
                    segment.error_message = f"Helsinki翻译质量不佳: {', '.join(quality_result.issues)}"

            except Exception as e:
                segment.error_message = f"Helsinki翻译错误: {e}"

        context.processing_log.append(f"Helsinki翻译完成，成功翻译 {translated_count} 个片段")
        return segments

    def _stage_tencent_translation(self, segments: List[TranslationSegment], context: TranslationContext) -> List[TranslationSegment]:
        """阶段5: 腾讯翻译API"""
        if not self.tencent_available:
            context.processing_log.append("腾讯翻译器不可用，跳过")
            return segments

        context.processing_log.append("开始腾讯翻译")

        # 收集需要翻译的片段
        pending_segments = [s for s in segments if s.result_status == TranslationResult.FAILED]
        if not pending_segments:
            context.processing_log.append("无需要腾讯翻译的片段")
            return segments

        # 批量翻译
        try:
            texts_to_translate = [s.original_text for s in pending_segments]
            translations = self.tencent_translator.translate_batch(texts_to_translate)

            translated_count = 0
            for i, segment in enumerate(pending_segments):
                if i < len(translations):
                    translation = translations[i]

                    # 质量检查
                    quality_result = self.quality_checker.check_translation_quality(segment.original_text, translation)
                    if quality_result.is_valid:
                        segment.translated_text = translation
                        segment.stage_completed = TranslationStage.TENCENT_TRANSLATION
                        segment.result_status = TranslationResult.SUCCESS
                        segment.confidence_score = quality_result.confidence_score
                        segment.chinese_remaining = quality_result.chinese_remaining_ratio
                        segment.translation_method = "腾讯翻译"
                        translated_count += 1
                    else:
                        segment.result_status = TranslationResult.PARTIAL
                        segment.translated_text = translation
                        segment.chinese_remaining = quality_result.chinese_remaining_ratio
                        segment.error_message = f"腾讯翻译质量不佳: {', '.join(quality_result.issues)}"

        except Exception as e:
            context.processing_log.append(f"腾讯批量翻译失败: {e}")
            for segment in pending_segments:
                segment.error_message = f"腾讯翻译错误: {e}"

        context.processing_log.append(f"腾讯翻译完成，成功翻译 {translated_count} 个片段")
        return segments

    def _stage_fine_segmentation_retry(self, segments: List[TranslationSegment], context: TranslationContext) -> List[TranslationSegment]:
        """阶段6: 细分重试机制"""
        context.processing_log.append("开始细分重试")

        # 找到需要细分的片段（翻译失败或质量不佳的）
        failed_segments = [s for s in segments if s.result_status in [TranslationResult.FAILED, TranslationResult.PARTIAL]]

        if not failed_segments:
            context.processing_log.append("无需要细分重试的片段")
            return segments

        new_segments = []
        retry_count = 0

        for segment in segments:
            if segment.result_status == TranslationResult.SUCCESS:
                new_segments.append(segment)
                continue

            # 尝试细分
            if len(segment.original_text) > 10:  # 只对较长的文本进行细分
                try:
                    # 更激进的分割策略
                    fine_segments = self._fine_segment_text(segment.original_text)

                    if len(fine_segments) > 1:
                        # 递归翻译细分后的片段
                        for fine_text in fine_segments:
                            fine_segment = TranslationSegment(original_text=fine_text)
                            fine_segment = self._translate_single_segment(fine_segment)
                            new_segments.append(fine_segment)
                        retry_count += 1
                    else:
                        # 无法进一步细分，标记为需要人工处理
                        segment.result_status = TranslationResult.NEEDS_MANUAL
                        new_segments.append(segment)

                except Exception as e:
                    segment.error_message = f"细分重试错误: {e}"
                    new_segments.append(segment)
            else:
                # 文本太短，无法细分，标记为需要人工处理
                segment.result_status = TranslationResult.NEEDS_MANUAL
                new_segments.append(segment)

        context.processing_log.append(f"细分重试完成，处理 {retry_count} 个片段")
        return new_segments

    def _translate_single_segment(self, segment: TranslationSegment) -> TranslationSegment:
        """翻译单个片段（用于细分重试）"""
        # 1. 词典匹配
        if self.tech_dict.is_technical_term(segment.original_text):
            translation = self.tech_dict.translate_term(segment.original_text)
            if translation and translation != segment.original_text:
                segment.translated_text = translation
                segment.result_status = TranslationResult.SUCCESS
                segment.translation_method = "技术词典"
                return segment

        # 2. Helsinki翻译
        if self.helsinki_available:
            try:
                translation = self.helsinki_translator.translate(segment.original_text)
                quality_result = self.quality_checker.check_translation_quality(segment.original_text, translation)
                if quality_result.is_valid:
                    segment.translated_text = translation
                    segment.result_status = TranslationResult.SUCCESS
                    segment.confidence_score = quality_result.confidence_score
                    segment.chinese_remaining = quality_result.chinese_remaining_ratio
                    segment.translation_method = "Helsinki-NLP"
                    return segment
            except Exception:
                pass

        # 3. 腾讯翻译
        if self.tencent_available:
            try:
                translation = self.tencent_translator.translate(segment.original_text)
                quality_result = self.quality_checker.check_translation_quality(segment.original_text, translation)
                if quality_result.is_valid:
                    segment.translated_text = translation
                    segment.result_status = TranslationResult.SUCCESS
                    segment.confidence_score = quality_result.confidence_score
                    segment.chinese_remaining = quality_result.chinese_remaining_ratio
                    segment.translation_method = "腾讯翻译"
                    return segment
            except Exception:
                pass

        # 翻译失败
        segment.result_status = TranslationResult.FAILED
        return segment

    def _fine_segment_text(self, text: str) -> List[str]:
        """更精细的文本分割"""
        try:
            # 使用智能分段器的精细分段功能
            return self.segmenter.fine_segment_text(text, target_length=10)
        except Exception as e:
            self.logger.debug(f"精细分段失败: {e}")
            # 备用简单分割
            return [text]



    def _calculate_quality_metrics(self, context: TranslationContext) -> Dict[str, float]:
        """计算翻译质量指标"""
        if not context.segments:
            return {}

        total_segments = len(context.segments)
        success_segments = len([s for s in context.segments if s.result_status == TranslationResult.SUCCESS])
        partial_segments = len([s for s in context.segments if s.result_status == TranslationResult.PARTIAL])
        failed_segments = len([s for s in context.segments if s.result_status == TranslationResult.FAILED])
        manual_segments = len([s for s in context.segments if s.result_status == TranslationResult.NEEDS_MANUAL])

        # 计算平均置信度
        confidence_scores = [s.confidence_score for s in context.segments if s.confidence_score > 0]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0

        # 计算中文残留比例
        total_chinese_remaining = sum(s.chinese_remaining for s in context.segments)
        avg_chinese_remaining = total_chinese_remaining / total_segments if total_segments > 0 else 0

        return {
            'success_rate': success_segments / total_segments,
            'partial_rate': partial_segments / total_segments,
            'failed_rate': failed_segments / total_segments,
            'manual_rate': manual_segments / total_segments,
            'avg_confidence': avg_confidence,
            'avg_chinese_remaining': avg_chinese_remaining,
            'total_segments': total_segments
        }



    def _create_empty_context(self) -> TranslationContext:
        """创建空的翻译上下文"""
        return TranslationContext(
            segments=[],
            original_format={},
            learning_suggestions=[],
            quality_metrics={},
            processing_log=[]
        )

    def format_output(self, context: TranslationContext, output_format: str = 'text') -> str:
        """格式化输出结果"""
        if output_format == 'text':
            return self._format_text_output(context)
        elif output_format == 'detailed':
            return self._format_detailed_output(context)
        elif output_format == 'json':
            return self._format_json_output(context)
        else:
            return self._format_text_output(context)

    def _format_text_output(self, context: TranslationContext) -> str:
        """格式化为纯文本输出"""
        if not context.segments:
            return ""

        # 合并翻译结果
        translated_parts = []
        for segment in context.segments:
            if segment.result_status == TranslationResult.SUCCESS:
                translated_parts.append(segment.translated_text)
            elif segment.result_status == TranslationResult.PARTIAL:
                translated_parts.append(segment.translated_text)
            else:
                # 翻译失败，保留原文
                translated_parts.append(segment.original_text)

        return ' '.join(translated_parts)

    def _format_detailed_output(self, context: TranslationContext) -> str:
        """格式化为详细输出"""
        output = []
        output.append("=" * 60)
        output.append("翻译结果详情")
        output.append("=" * 60)

        # 翻译结果
        output.append("\n📝 翻译结果:")
        output.append(self._format_text_output(context))

        # 质量指标
        if context.quality_metrics:
            output.append(f"\n📊 质量指标:")
            metrics = context.quality_metrics
            output.append(f"  成功率: {metrics.get('success_rate', 0):.1%}")
            output.append(f"  部分成功率: {metrics.get('partial_rate', 0):.1%}")
            output.append(f"  失败率: {metrics.get('failed_rate', 0):.1%}")
            output.append(f"  需人工处理: {metrics.get('manual_rate', 0):.1%}")
            output.append(f"  平均置信度: {metrics.get('avg_confidence', 0):.2f}")
            output.append(f"  总片段数: {metrics.get('total_segments', 0)}")

        # 学习建议
        if context.learning_suggestions:
            output.append(f"\n💡 学习建议:")
            for suggestion in context.learning_suggestions:
                if suggestion['type'] == 'term_suggestion':
                    output.append(f"  术语: \"{suggestion['chinese']}\" -> \"{suggestion['english']}\"")
                elif suggestion['type'] == 'improvement_suggestion':
                    output.append(f"  建议: {suggestion['message']}")

        # 处理日志
        if context.processing_log:
            output.append(f"\n📋 处理日志:")
            for log_entry in context.processing_log[-5:]:  # 只显示最后5条
                output.append(f"  {log_entry}")

        return '\n'.join(output)

    def _format_json_output(self, context: TranslationContext) -> str:
        """格式化为JSON输出"""
        import json

        result = {
            'translated_text': self._format_text_output(context),
            'segments': [
                {
                    'original': s.original_text,
                    'translated': s.translated_text,
                    'status': s.result_status.value,
                    'confidence': s.confidence_score,
                    'method': s.translation_method
                }
                for s in context.segments
            ],
            'quality_metrics': context.quality_metrics,
            'learning_suggestions': context.learning_suggestions,
            'processing_log': context.processing_log
        }

        return json.dumps(result, ensure_ascii=False, indent=2)

    def get_untranslated_content(self, context: TranslationContext) -> List[str]:
        """获取未翻译的内容列表"""
        untranslated = []

        for segment in context.segments:
            if segment.result_status in [TranslationResult.FAILED, TranslationResult.NEEDS_MANUAL]:
                untranslated.append(segment.original_text)

        return untranslated

    def get_learning_report(self, context: TranslationContext, format_type: str = 'text') -> str:
        """获取学习报告"""
        learning_report = self.learning_advisor.analyze_translation_session(context)
        return self.learning_advisor.format_learning_report(learning_report, format_type)

    def get_translation_summary(self, context: TranslationContext) -> Dict[str, Any]:
        """获取翻译摘要"""
        return {
            'translated_text': self.format_output(context, 'text'),
            'quality_metrics': context.quality_metrics,
            'total_segments': len(context.segments),
            'success_segments': len([s for s in context.segments if s.result_status == TranslationResult.SUCCESS]),
            'failed_segments': len([s for s in context.segments if s.result_status == TranslationResult.FAILED]),
            'manual_segments': len([s for s in context.segments if s.result_status == TranslationResult.NEEDS_MANUAL]),
            'untranslated_content': self.get_untranslated_content(context),
            'learning_suggestions_count': len(context.learning_suggestions),
            'processing_log': context.processing_log
        }
