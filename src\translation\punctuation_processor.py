#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标点符号处理器
处理中英文标点符号转换和智能分句
"""

import re
from typing import List, Tuple, Dict
from ..utils import get_logger

logger = get_logger(__name__)

class PunctuationProcessor:
    """
    标点符号处理器

    对外统一接口：
    - split_text(text) -> List[str]  # 主要分割接口
    - convert_punctuation(text) -> str  # 标点转换接口

    其他方法均为内部辅助方法，不应直接调用
    """

    def __init__(self):
        # 中英文标点符号映射
        self.punctuation_mapping = {
            # 基础标点
            '，': ',',
            '。': '.',
            '；': ';',
            '：': ':',
            '！': '!',
            '？': '?',
            
            # 引号
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            
            # 括号
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '《': '<',
            '》': '>',
            
            # 其他符号
            '、': ',',  # 顿号转逗号
            '…': '...',
            '——': '--',
            '－': '-',
        }
        
        # 分割配置（统一管理）
        self.split_config = {
            'min_segment_length': 2,
            'semantic_keywords': [
                '适用于', '用于', '具有', '包含', '应该', '需要', '可以',
                '如果', '当', '由于', '因为', '和', '与', '或', '及'
            ]
        }
        
        # 需要保留的特殊模式
        self.preserve_patterns = [
            r'\d+[°℃]',           # 温度：23℃
            r'\d+%',              # 百分比：60%
            r'\d+±\d+',           # 误差范围：±1
            r'\d+~\d+',           # 范围：25~45
            r'\d+/\d+',           # 分数或比例：A/B
            r'[A-Z]+-\d+',        # 产品型号：FM-102
            r'\d+kg',             # 重量单位
            r'\d+g',              # 重量单位
            r'\d+h',              # 时间单位
            r'\d+min',            # 时间单位
        ]
    
    def convert_punctuation(self, text: str) -> str:
        """转换中文标点为英文标点"""
        if not text:
            return text
        
        result = text
        
        # 保护特殊模式
        protected_patterns = []
        placeholder_map = {}

        for i, pattern in enumerate(self.preserve_patterns):
            matches = list(re.finditer(pattern, result))
            for j, match in enumerate(reversed(matches)):  # 从后往前替换，避免位置偏移
                placeholder = f"__PROTECTED_{i}_{j}__"
                protected_text = match.group()
                protected_patterns.append(protected_text)
                placeholder_map[placeholder] = protected_text
                result = result[:match.start()] + placeholder + result[match.end():]

        # 转换标点符号
        for chinese_punct, english_punct in self.punctuation_mapping.items():
            result = result.replace(chinese_punct, english_punct)

        # 恢复保护的模式
        for placeholder, protected_text in placeholder_map.items():
            result = result.replace(placeholder, protected_text)
        
        return result

    def split_text(self, text: str) -> List[str]:
        """
        统一的对外分割接口
        这是唯一推荐的分割方法，其他方法仅供内部使用

        Args:
            text: 要分割的中文文本

        Returns:
            List[str]: 分割后的文本片段列表，已清理标点符号
        """
        return self.smart_split_sentences(text)

    def smart_split_sentences(self, text: str) -> List[str]:
        """智能分句 - 统一分割策略"""
        if not text:
            return []

        # 1. 标点转换
        processed_text = self.convert_punctuation(text)

        # 2. 统一分割处理
        segments = self._unified_split(processed_text)

        # 3. 清理和过滤
        return self._clean_and_filter_segments(segments)

    def _unified_split(self, text: str) -> List[str]:
        """统一分割处理 - 简化版"""
        # 分割策略：从粗到细（更激进的阈值）
        strategies = [
            (self._split_sentences, 0),           # 句子分割
            (self._split_brackets, 0),            # 括号分割
            (self._split_clauses, 5),             # 子句分割（降低阈值）
            (self._split_spaces, 8),              # 空格分割（降低阈值）
            (self._split_semantic, 20),           # 语义分割（降低阈值）
        ]

        segments = [text]

        for split_func, min_length in strategies:
            new_segments = []
            for segment in segments:
                if len(segment) > min_length:
                    new_segments.extend(split_func(segment))
                else:
                    new_segments.append(segment)
            segments = new_segments

        return segments

    def _split_sentences(self, text: str) -> List[str]:
        """分割句子"""
        return self._split_by_any_delimiter(text, ['.', '!', '?', ';'])

    def _split_brackets(self, text: str) -> List[str]:
        """分割括号"""
        return self._extract_and_split_brackets(text)

    def _split_clauses(self, text: str) -> List[str]:
        """分割子句"""
        return self._split_by_any_delimiter(text, [',', '、', ':', '或', '和', '与'])

    def _split_spaces(self, text: str) -> List[str]:
        """分割空格"""
        return self._split_by_any_delimiter(text, [' ', '\t'])

    def _split_semantic(self, text: str) -> List[str]:
        """语义分割"""
        return self._split_by_semantic_units(text)

    def _split_by_any_delimiter(self, text: str, delimiters: List[str]) -> List[str]:
        """按任意分隔符分割（递归处理）"""
        # 按优先级尝试分割
        for delimiter in delimiters:
            if delimiter in text:
                parts = self._split_and_clean_delimiter(text, delimiter)
                if len(parts) > 1:
                    # 递归处理每个部分，寻找其他分隔符
                    result = []
                    for part in parts:
                        # 特殊处理：如果是连接词，直接添加，不再递归
                        if part in ['或', '和', '与']:
                            result.append(part)
                        else:
                            # 对其他部分继续尝试其他分隔符
                            sub_parts = self._split_by_any_delimiter(part, [d for d in delimiters if d != delimiter])
                            result.extend(sub_parts)
                    return result
        return [text]

    def _split_and_clean_delimiter(self, text: str, delimiter: str) -> List[str]:
        """分割并智能清理分隔符"""
        if delimiter not in text:
            return [text]

        parts = text.split(delimiter)
        result = []

        for i, part in enumerate(parts):
            part = part.strip()
            if part:
                # 智能分隔符处理策略
                if delimiter in [',', '、']:
                    # 逗号和顿号：完全移除
                    result.append(part)
                elif delimiter in ['或', '和', '与']:
                    # 连接词：保留为独立片段
                    if i == 0 and len(parts) > 1:
                        # 第一部分
                        result.append(part)
                        # 添加连接词作为独立片段
                        result.append(delimiter)
                    else:
                        # 后续部分
                        result.append(part)
                elif delimiter == ':':
                    # 冒号：特殊处理，确保分割
                    if i == 0 and len(parts) > 1:
                        # 第一部分：保留冒号作为标题
                        result.append(part + delimiter)
                    else:
                        # 后续部分：不保留冒号
                        result.append(part)
                elif delimiter in ['.', '!', '?', ';']:
                    # 句子结束符：移除
                    result.append(part)
                elif delimiter in [' ', '\t']:
                    # 空格：直接分割，不保留
                    result.append(part)
                else:
                    # 其他分隔符：保持原样
                    result.append(part)

        return result if result else [text]

    def _clean_and_filter_segments(self, segments: List[str]) -> List[str]:
        """清理和过滤片段"""
        cleaned = []

        for segment in segments:
            # 基础清理
            segment = segment.strip()

            # 清理首尾标点
            segment = self._clean_punctuation(segment)

            # 过滤条件（连接词例外）
            if segment and (len(segment) >= self.split_config['min_segment_length'] or segment in ['或', '和', '与']):
                cleaned.append(segment)

        return cleaned

    def _clean_punctuation(self, text: str) -> str:
        """清理首尾标点符号"""
        if not text:
            return text

        # 清理开头标点
        leading_punct = [',', ';', '、', '，', '；']
        for punct in leading_punct:
            if text.startswith(punct):
                text = text[1:].strip()
                break

        # 清理结尾标点（除了冒号，因为冒号通常表示标题）
        trailing_punct = [',', ';', '、', '，', '；']
        for punct in trailing_punct:
            if text.endswith(punct):
                text = text[:-1].strip()
                break

        return text



    def _extract_and_split_brackets(self, text: str) -> List[str]:
        """提取并分割括号内容"""
        if not text:
            return [text]

        # 括号类型配置
        bracket_pairs = [
            ('(', ')'), ('（', '）'),  # 圆括号
            ('[', ']'), ('【', '】'),  # 方括号
        ]

        # 尝试分割括号
        for open_br, close_br in bracket_pairs:
            if open_br in text and close_br in text:
                parts = []
                temp_text = text

                while open_br in temp_text and close_br in temp_text:
                    start_idx = temp_text.find(open_br)
                    end_idx = temp_text.find(close_br, start_idx)

                    if start_idx != -1 and end_idx != -1:
                        # 括号前的内容
                        before = temp_text[:start_idx].strip()
                        if before:
                            parts.append(before)

                        # 括号内容（包括括号）
                        bracket_content = temp_text[start_idx:end_idx+1].strip()
                        if bracket_content:
                            parts.append(bracket_content)

                        # 继续处理剩余部分
                        temp_text = temp_text[end_idx+1:]
                    else:
                        break

                # 添加最后剩余的部分
                if temp_text.strip():
                    parts.append(temp_text.strip())

                if len(parts) > 1:
                    return parts

        return [text]



    def _split_by_semantic_units(self, text: str) -> List[str]:
        """按语义单元分割"""
        # 使用配置中的语义关键词
        for keyword in self.split_config['semantic_keywords']:
            if keyword in text:
                parts = text.split(keyword, 1)
                if len(parts) == 2 and len(parts[0]) >= 3 and len(parts[1]) >= 3:
                    return [parts[0].strip(), keyword + parts[1].strip()]

        return [text]
    
    def _split_by_delimiters(self, text: str, delimiters: List[str]) -> List[str]:
        """按指定分隔符分割文本"""
        if not text or not delimiters:
            return [text]
        
        # 构建分割正则表达式
        delimiter_pattern = '|'.join(re.escape(d) for d in delimiters)
        
        # 分割但保留分隔符
        parts = re.split(f'({delimiter_pattern})', text)
        
        # 重新组合，将分隔符附加到前一部分
        sentences = []
        current_sentence = ""
        
        for part in parts:
            if part in delimiters:
                current_sentence += part
                if current_sentence.strip():
                    sentences.append(current_sentence.strip())
                current_sentence = ""
            else:
                current_sentence += part
        
        # 添加最后一部分
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        return sentences
    
    def process_for_translation(self, text: str) -> Tuple[List[str], Dict[str, str]]:
        """
        为翻译处理文本
        返回: (句子列表, 原始映射字典)
        """
        if not text:
            return [], {}
        
        # 使用统一分割接口
        sentences = self.split_text(text)
        
        # 创建原始映射，用于后续重组
        original_mapping = {}
        for i, sentence in enumerate(sentences):
            original_mapping[f"SENTENCE_{i}"] = sentence
        
        logger.debug(f"文本分句完成: {len(sentences)} 个句子")
        
        return sentences, original_mapping
    
    def reconstruct_text(self, translated_sentences: List[str], original_mapping: Dict[str, str]) -> str:
        """重构翻译后的文本"""
        if not translated_sentences:
            return ""
        
        # 简单重组，用空格连接
        result = " ".join(translated_sentences)
        
        # 清理多余的空格
        result = re.sub(r'\s+', ' ', result)
        
        # 修复标点符号前的空格
        result = re.sub(r'\s+([,.;:!?])', r'\1', result)
        
        # 修复括号内的空格
        result = re.sub(r'\(\s+', '(', result)
        result = re.sub(r'\s+\)', ')', result)
        
        return result.strip()
    
    def analyze_sentence_complexity(self, text: str) -> Dict[str, any]:
        """分析句子复杂度"""
        if not text:
            return {"complexity": "empty", "score": 0}
        
        # 计算复杂度指标
        length = len(text)
        chinese_chars = len(re.findall(r'[\u4e00-\u9fa5]', text))
        punctuation_count = len(re.findall(r'[，。；：！？、]', text))
        number_count = len(re.findall(r'\d+', text))
        
        # 复杂度评分
        complexity_score = 0
        
        # 长度因子
        if length > 100:
            complexity_score += 3
        elif length > 50:
            complexity_score += 2
        elif length > 20:
            complexity_score += 1
        
        # 标点符号因子
        complexity_score += min(punctuation_count, 3)
        
        # 数字和单位因子
        complexity_score += min(number_count, 2)
        
        # 确定复杂度等级
        if complexity_score >= 6:
            complexity = "high"
        elif complexity_score >= 3:
            complexity = "medium"
        else:
            complexity = "low"
        
        return {
            "complexity": complexity,
            "score": complexity_score,
            "length": length,
            "chinese_chars": chinese_chars,
            "punctuation_count": punctuation_count,
            "number_count": number_count,
            "recommend_split": complexity_score >= 4
        }
    
    def get_optimal_split_strategy(self, text: str) -> str:
        """获取最优分割策略"""
        analysis = self.analyze_sentence_complexity(text)
        
        if analysis["complexity"] == "high":
            return "sentence_and_clause"  # 按句子和子句分割
        elif analysis["complexity"] == "medium":
            return "sentence_only"       # 仅按句子分割
        else:
            return "no_split"           # 不分割
