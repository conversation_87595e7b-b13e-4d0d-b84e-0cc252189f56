#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一翻译系统集成测试
验证重构后的翻译系统功能完整性和性能提升
"""

import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.translation.unified_translator import UnifiedTranslator
from src.translation.translation_main import TranslationSystem
from src.utils import get_logger

logger = get_logger(__name__)


def test_basic_translation():
    """测试基础翻译功能"""
    print("\n" + "=" * 60)
    print("🧪 测试基础翻译功能")
    print("=" * 60)
    
    try:
        # 初始化翻译器
        translator = UnifiedTranslator()
        
        # 测试用例
        test_cases = [
            "固化条件：25℃，24小时",
            "拉伸强度：≥15MPa",
            "储存条件：阴凉干燥处，避免阳光直射",
            "产品型号：FM-2000胶粘剂",
            "工艺流程：混合→搅拌→浇注→固化"
        ]
        
        print(f"测试 {len(test_cases)} 个用例...")
        
        for i, text in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}:")
            print(f"原文: {text}")
            
            start_time = time.time()
            context = translator.translate_text(text)
            processing_time = time.time() - start_time
            
            translated = translator.format_output(context, 'text')
            print(f"译文: {translated}")
            print(f"处理时间: {processing_time:.2f}秒")
            
            # 显示质量指标
            if context.quality_metrics:
                metrics = context.quality_metrics
                print(f"成功率: {metrics.get('success_rate', 0):.1%}")
                print(f"片段数: {metrics.get('total_segments', 0)}")
        
        print("\n✅ 基础翻译功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 基础翻译功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_segmentation_quality():
    """测试分段质量"""
    print("\n" + "=" * 60)
    print("🧪 测试分段质量")
    print("=" * 60)
    
    try:
        from src.translation.smart_segmenter import SmartSegmenter
        
        segmenter = SmartSegmenter()
        
        # 测试复杂文本
        complex_text = "本产品为双组分环氧树脂胶粘剂，具有优异的粘接强度和耐候性能，适用于金属、塑料、陶瓷等多种材料的粘接，固化条件为25℃下24小时，储存期限为12个月。"
        
        print(f"原文: {complex_text}")
        print(f"原文长度: {len(complex_text)} 字符")
        
        # 测试不同分段策略
        segments = segmenter.segment_text(complex_text, max_length=20)
        print(f"\n分段结果 ({len(segments)} 个片段):")
        for i, segment in enumerate(segments, 1):
            print(f"  {i}. {segment} ({len(segment)} 字符)")
        
        # 评估分段质量
        quality = segmenter.get_segmentation_quality(segments, target_length=20)
        print(f"\n分段质量评估:")
        print(f"  质量分数: {quality['quality_score']:.2f}")
        print(f"  平均长度: {quality['avg_length']:.1f} 字符")
        print(f"  理想片段比例: {quality['ideal_ratio']:.1%}")
        
        # 测试精细分段
        fine_segments = segmenter.fine_segment_text(complex_text, target_length=10)
        print(f"\n精细分段结果 ({len(fine_segments)} 个片段):")
        for i, segment in enumerate(fine_segments[:10], 1):  # 只显示前10个
            print(f"  {i}. {segment} ({len(segment)} 字符)")
        
        print("\n✅ 分段质量测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 分段质量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_quality_checker():
    """测试质量检查器"""
    print("\n" + "=" * 60)
    print("🧪 测试质量检查器")
    print("=" * 60)
    
    try:
        from src.translation.quality_checker import QualityChecker
        
        checker = QualityChecker()
        
        # 测试用例：原文和不同质量的译文
        test_cases = [
            ("固化条件：25℃", "Curing Conditions: 25°C", "高质量翻译"),
            ("拉伸强度：≥15MPa", "拉伸强度：≥15MPa", "未翻译"),
            ("储存条件", "Storage Conditions for Materials and Equipment", "过度翻译"),
            ("产品型号", "Product Model Number Type", "质量中等"),
            ("工艺流程", "", "翻译为空")
        ]
        
        print(f"测试 {len(test_cases)} 个质量检查用例...")
        
        for i, (original, translated, description) in enumerate(test_cases, 1):
            print(f"\n测试用例 {i} ({description}):")
            print(f"原文: {original}")
            print(f"译文: {translated}")
            
            result = checker.check_translation_quality(original, translated)
            
            print(f"质量检查结果:")
            print(f"  有效性: {'✅' if result.is_valid else '❌'}")
            print(f"  置信度: {result.confidence_score:.2f}")
            print(f"  中文残留: {result.chinese_remaining_ratio:.1%}")
            
            if result.issues:
                print(f"  问题: {', '.join(result.issues)}")
            
            if result.suggestions:
                print(f"  建议: {', '.join(result.suggestions)}")
        
        print("\n✅ 质量检查器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 质量检查器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_learning_advisor():
    """测试学习建议系统"""
    print("\n" + "=" * 60)
    print("🧪 测试学习建议系统")
    print("=" * 60)
    
    try:
        # 使用统一翻译器进行翻译并生成学习建议
        translator = UnifiedTranslator()
        
        # 包含一些技术术语和可能翻译失败的文本
        test_text = "本产品采用先进的纳米技术，具有超强的粘接性能和优异的耐老化特性，适用于航空航天、汽车制造等高端应用领域。"
        
        print(f"测试文本: {test_text}")
        
        # 执行翻译
        context = translator.translate_text(test_text)
        
        # 获取学习报告
        learning_report = translator.get_learning_report(context, 'text')
        print(f"\n学习报告:")
        print(learning_report)
        
        # 获取翻译摘要
        summary = translator.get_translation_summary(context)
        print(f"\n翻译摘要:")
        print(f"  总片段数: {summary['total_segments']}")
        print(f"  成功片段: {summary['success_segments']}")
        print(f"  失败片段: {summary['failed_segments']}")
        print(f"  学习建议数: {summary['learning_suggestions_count']}")
        
        print("\n✅ 学习建议系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 学习建议系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_system_integration():
    """测试系统集成"""
    print("\n" + "=" * 60)
    print("🧪 测试系统集成")
    print("=" * 60)
    
    try:
        # 初始化翻译系统
        system = TranslationSystem(cloud_only=False)
        
        # 获取系统状态
        status = system.get_system_status()
        print(f"系统状态:")
        print(f"  运行模式: {status['mode']}")
        print(f"  本地翻译: {'可用' if status['helsinki_available'] else '不可用'}")
        print(f"  云端翻译: {'可用' if status['tencent_available'] else '不可用'}")
        print(f"  支持格式: {', '.join(status['supported_formats'])}")
        
        # 测试文本翻译
        test_text = "产品规格：长度100mm，宽度50mm，厚度2mm"
        print(f"\n测试文本翻译:")
        print(f"原文: {test_text}")
        
        result = system.translate_text(test_text, show_details=False)
        print(f"译文: {result}")
        
        # 测试翻译分析
        analysis = system.get_translation_analysis(test_text)
        print(f"\n翻译分析:")
        print(analysis)
        
        print("\n✅ 系统集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_comparison():
    """测试性能对比"""
    print("\n" + "=" * 60)
    print("🧪 测试性能对比")
    print("=" * 60)
    
    try:
        # 测试文本
        test_texts = [
            "固化条件：25℃，24小时",
            "拉伸强度：≥15MPa，断裂伸长率：≥200%",
            "储存条件：阴凉干燥处，避免阳光直射，储存期限12个月",
            "产品型号：FM-2000双组分环氧树脂胶粘剂",
            "工艺流程：表面处理→涂胶→贴合→加压→固化→检验"
        ]
        
        # 使用新的统一翻译器
        print("使用统一翻译器:")
        translator = UnifiedTranslator()
        
        total_time = 0
        total_segments = 0
        total_success = 0
        
        for i, text in enumerate(test_texts, 1):
            start_time = time.time()
            context = translator.translate_text(text)
            processing_time = time.time() - start_time
            
            total_time += processing_time
            total_segments += context.quality_metrics.get('total_segments', 0)
            total_success += context.quality_metrics.get('success_rate', 0) * context.quality_metrics.get('total_segments', 0)
            
            print(f"  文本 {i}: {processing_time:.3f}秒, 片段数: {context.quality_metrics.get('total_segments', 0)}")
        
        avg_success_rate = total_success / total_segments if total_segments > 0 else 0
        
        print(f"\n统一翻译器性能统计:")
        print(f"  总处理时间: {total_time:.3f}秒")
        print(f"  平均处理时间: {total_time/len(test_texts):.3f}秒/文本")
        print(f"  总片段数: {total_segments}")
        print(f"  平均成功率: {avg_success_rate:.1%}")
        print(f"  处理速度: {total_segments/total_time:.1f}片段/秒")
        
        print("\n✅ 性能对比测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 统一翻译系统集成测试")
    print("=" * 80)
    
    # 测试项目列表
    tests = [
        ("基础翻译功能", test_basic_translation),
        ("分段质量", test_segmentation_quality),
        ("质量检查器", test_quality_checker),
        ("学习建议系统", test_learning_advisor),
        ("系统集成", test_system_integration),
        ("性能对比", test_performance_comparison),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 测试结果汇总
    print("\n" + "=" * 80)
    print(f"📊 测试结果汇总: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一翻译系统重构成功！")
        print("\n💡 重构成果:")
        print("  ✅ 消除了代码重复，提高了可维护性")
        print("  ✅ 统一了翻译流程，遵循第一性原则")
        print("  ✅ 优化了分段策略，提高了翻译质量")
        print("  ✅ 集成了质量检查，确保翻译准确性")
        print("  ✅ 完善了学习建议，支持持续改进")
        print("  ✅ 简化了文档处理，统一了格式保留")
        
        print("\n🚀 使用方法:")
        print("  python src/translation/translation_main.py")
        
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        print("\n🔧 建议:")
        print("1. 检查依赖包是否完整安装")
        print("2. 确认配置文件是否正确")
        print("3. 验证网络连接和API配置")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
