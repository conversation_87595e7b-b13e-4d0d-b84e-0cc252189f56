#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本
使用PyInstaller将程序打包为exe文件
"""

import os
import sys
import shutil
from pathlib import Path
import PyInstaller.__main__

def build_exe():
    """构建exe文件"""
    print("开始构建智能汇总表生成器...")
    
    # 清理之前的构建
    build_dir = Path("build")
    dist_dir = Path("dist")
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    # PyInstaller参数
    args = [
        'main.py',
        '--name=智能汇总表生成器',
        '--onefile',
        '--windowed',
        '--icon=icon.ico',  # 如果有图标文件
        '--add-data=config.json;.',
        '--add-data=src;src',
        '--hidden-import=pdfplumber',
        '--hidden-import=docx',
        '--hidden-import=openpyxl',
        '--hidden-import=rapidfuzz',
        '--hidden-import=colorlog',
        '--hidden-import=tqdm',
        '--clean',
        '--noconfirm'
    ]
    
    # 如果没有图标文件，移除图标参数
    if not Path("icon.ico").exists():
        args = [arg for arg in args if not arg.startswith('--icon')]
    
    try:
        PyInstaller.__main__.run(args)
        print("✅ 构建完成！")
        print(f"可执行文件位置: {dist_dir / '智能汇总表生成器.exe'}")
        
        # 复制配置文件到dist目录
        if Path("config.json").exists():
            shutil.copy2("config.json", dist_dir)
            print("✅ 配置文件已复制到dist目录")
        
        # 创建使用说明
        create_readme(dist_dir)
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        return False
    
    return True

def create_readme(dist_dir):
    """创建使用说明文件"""
    readme_content = """# 智能汇总表生成器 v2.0

## 使用方法

1. 将PDF和Word技术参数文档放入 `PDF` 文件夹
2. 双击运行 `智能汇总表生成器.exe`
3. 查看生成的 `智能汇总表.xlsx` 文件

## 文件说明

- `智能汇总表生成器.exe`: 主程序
- `config.json`: 配置文件（可自定义字段映射）
- `logs/`: 日志文件夹
- `learned_mappings.json`: 学习的映射关系（自动生成）

## 支持的文件格式

- PDF文件 (.pdf)
- Word文档 (.docx)

## 配置说明

编辑 `config.json` 文件可以自定义：
- 输入文件夹路径
- 输出文件名
- 字段映射关系
- 参数匹配规则

## 技术支持

如有问题，请查看 `logs` 文件夹中的日志文件。
"""
    
    readme_file = dist_dir / "使用说明.txt"
    with open(readme_file, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 使用说明已创建")

def create_installer():
    """创建安装程序（可选）"""
    # 这里可以添加创建NSIS或Inno Setup安装程序的代码
    pass

if __name__ == "__main__":
    if build_exe():
        print("\n🎉 打包完成！")
        print("可以在 dist 文件夹中找到可执行文件。")
    else:
        print("\n❌ 打包失败！")
        sys.exit(1)
