#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于配置的数据提取器
直接使用config.json的param_mapping进行数据匹配和提取，简化整个流程
"""

import os
import re
from typing import Dict, List, Optional, Any, Tuple
from rapidfuzz import fuzz
from ..extraction import PDFExtractor, DocxExtractor
from ..utils import get_logger


class ConfigBasedExtractor:
    """基于配置的数据提取器
    
    核心思想：
    1. 直接使用config.json的param_mapping进行参数匹配
    2. 统一处理AB组件逻辑，避免重复代码
    3. 简化数据流，提高可维护性
    """
    
    def __init__(self, config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # 获取配置
        self.param_mapping = config.get_param_mapping()
        self.column_mapping = config.get_column_mapping()
        self.ab_parameters = config.get_ab_parameters()
        
        # 初始化文档提取器
        self.pdf_extractor = PDFExtractor()
        self.docx_extractor = DocxExtractor()
        
        # 构建反向映射用于快速查找
        self._build_reverse_mappings()
        
        self.logger.info(f"配置驱动提取器初始化完成，支持 {len(self.param_mapping)} 个参数")
    
    def _build_reverse_mappings(self):
        """构建反向映射表，用于快速参数匹配"""
        self.alias_to_standard = {}
        
        for standard_param, aliases in self.param_mapping.items():
            for alias in aliases:
                # 原始别名
                self.alias_to_standard[alias.lower()] = standard_param
                # 清理后的别名（去除括号、数字等）
                cleaned_alias = self._clean_text_for_matching(alias)
                if cleaned_alias:
                    self.alias_to_standard[cleaned_alias.lower()] = standard_param
        
        self.logger.debug(f"构建反向映射表完成，包含 {len(self.alias_to_standard)} 个映射")
    
    def extract_from_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """从文件提取数据"""
        try:
            self.logger.info(f"开始提取文件: {os.path.basename(file_path)}")
            
            # 根据文件类型选择提取器
            if file_path.lower().endswith('.pdf'):
                extracted_content = self.pdf_extractor.extract_content(file_path)
            elif file_path.lower().endswith(('.docx', '.doc')):
                extracted_content = self.docx_extractor.extract_content(file_path)
            else:
                self.logger.error(f"不支持的文件类型: {file_path}")
                return None
            
            if not extracted_content:
                self.logger.warning(f"文件内容为空: {file_path}")
                return None
            
            # 提取产品信息
            product_info = self._extract_product_info(extracted_content, file_path)
            
            # 提取并匹配参数
            raw_parameters = self._extract_raw_parameters(extracted_content)
            matched_parameters = self._match_parameters(raw_parameters)
            
            # 处理AB组件
            final_parameters = self._process_ab_components(matched_parameters, product_info.get('product_name', ''))
            
            result = {
                'file_path': file_path,
                'product_name': product_info.get('product_name', ''),
                'product_model': product_info.get('product_model', ''),
                'parameters': final_parameters
            }
            
            self.logger.info(f"提取完成: {os.path.basename(file_path)}, 参数数量: {len(final_parameters)}")
            return result
            
        except Exception as e:
            self.logger.error(f"文件提取失败 {file_path}: {e}")
            return None
    
    def _extract_product_info(self, content, file_path: str) -> Dict[str, str]:
        """提取产品信息"""
        product_info = {'product_name': '', 'product_model': ''}
        
        # 从文件名提取产品型号
        filename = os.path.basename(file_path)
        filename_without_ext = os.path.splitext(filename)[0]
        
        # 常见的产品型号模式
        model_patterns = [
            r'(FM-[A-Z0-9-]+)',
            r'(\d+-\d+)',
            r'([A-Z]+\d+[A-Z]*)',
        ]
        
        for pattern in model_patterns:
            match = re.search(pattern, filename_without_ext)
            if match:
                product_info['product_model'] = match.group(1)
                product_info['product_name'] = match.group(1)
                break
        
        # 从文档内容中提取产品信息
        text_content = content.text if hasattr(content, 'text') else str(content)
        
        product_patterns = [
            r'产品名称[：:]\s*([^\n\r]+)',
            r'产品型号[：:]\s*([^\n\r]+)',
            r'型号[：:]\s*([^\n\r]+)',
            r'Product[：:]\s*([^\n\r]+)',
            r'Model[：:]\s*([^\n\r]+)',
        ]
        
        for pattern in product_patterns:
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                extracted_name = match.group(1).strip()
                if extracted_name and not product_info['product_name']:
                    product_info['product_name'] = extracted_name
                    product_info['product_model'] = extracted_name
                break
        
        # 如果都没有找到，使用文件名
        if not product_info['product_name']:
            product_info['product_name'] = filename_without_ext
            product_info['product_model'] = filename_without_ext
        
        return product_info
    
    def _extract_raw_parameters(self, content) -> Dict[str, str]:
        """提取原始参数"""
        parameters = {}
        
        # 从表格中提取参数
        if hasattr(content, 'tables') and content.tables:
            for table in content.tables:
                table_params = self._extract_from_table(table)
                parameters.update(table_params)
        
        # 从文本中提取参数
        if hasattr(content, 'text'):
            text_params = self._extract_from_text(content.text)
            # 文本参数优先级较低，只在表格中没有找到时才使用
            for key, value in text_params.items():
                if key not in parameters:
                    parameters[key] = value
        
        self.logger.debug(f"提取到原始参数: {len(parameters)} 个")
        return parameters
    
    def _extract_from_table(self, table) -> Dict[str, str]:
        """从表格中提取参数"""
        parameters = {}
        
        if not hasattr(table, 'data') or not table.data:
            return parameters
        
        table_data = table.data
        
        # 查找参数列和值列
        param_col, value_cols = self._find_table_columns(table_data)
        
        if param_col is None:
            return parameters
        
        # 检查是否为AB表格
        is_ab_table = self._is_ab_table(table_data)

        # 遍历表格行提取参数
        for row in table_data:
            if len(row) <= param_col:
                continue

            param_name = str(row[param_col]).strip()
            if not param_name or param_name in ['测试项目', '项目', 'Item', 'Parameter', '参数']:
                continue

            # 提取参数值
            if is_ab_table:
                # AB表格：第三列为A组分，第四列为B组分
                # 检查是否为AB核心参数
                ab_core_params = ['外观', '粘度', '密度']
                if any(core_param == param_name for core_param in ab_core_params):
                    # A组分（第三列，索引2）
                    if len(row) > 2:
                        value_a = str(row[2]).strip()
                        if value_a and not self._is_unit_or_header(value_a):
                            parameters[f"{param_name}(A)"] = value_a
                            self.logger.debug(f"AB参数提取A: {param_name}(A) = {value_a}")

                    # B组分（第四列，索引3）
                    if len(row) > 3:
                        value_b = str(row[3]).strip()
                        if value_b and not self._is_unit_or_header(value_b):
                            parameters[f"{param_name}(B)"] = value_b
                            self.logger.debug(f"AB参数提取B: {param_name}(B) = {value_b}")
                else:
                    # 非AB核心参数，按普通逻辑处理
                    best_value = None
                    best_score = 0

                    for value_col in value_cols:
                        if value_col < len(row):
                            value = str(row[value_col]).strip()
                            if value and not self._is_unit_or_header(value):
                                score = self._calculate_value_score(param_name, value)
                                if score > best_score:
                                    best_score = score
                                    best_value = value

                    if best_value:
                        parameters[param_name] = best_value
                        self.logger.debug(f"AB表格普通参数提取: {param_name} = {best_value}")
            else:
                # 普通表格：智能选择最佳值
                best_value = None
                best_score = 0

                for value_col in value_cols:
                    if value_col < len(row):
                        value = str(row[value_col]).strip()
                        if value and not self._is_unit_or_header(value):
                            score = self._calculate_value_score(param_name, value)
                            if score > best_score:
                                best_score = score
                                best_value = value

                if best_value:
                    parameters[param_name] = best_value
                    self.logger.debug(f"普通参数提取: {param_name} = {best_value} (评分: {best_score})")
        
        return parameters
    
    def _find_table_columns(self, table_data: List[List]) -> Tuple[Optional[int], List[int]]:
        """查找表格中的参数列和值列"""
        if not table_data:
            return None, []

        import re

        param_col = None
        value_cols = []

        # 检查前几行找到列结构
        for row_idx, row in enumerate(table_data[:3]):
            for col_idx, cell in enumerate(row):
                cell_text = str(cell).strip().lower()

                # 查找参数列（通常是第一列）
                if param_col is None and any(keyword in cell_text for keyword in ['项目', 'parameter', '参数', '测试项目']):
                    param_col = col_idx
                    continue

                # 查找值列，排除明显的非值列
                if col_idx != param_col and cell_text:
                    # 排除单位列、标准列等
                    if any(keyword in cell_text for keyword in ['单位', 'unit', '条件', '标准', 'standard', '执行标准', '测试方法']):
                        continue

                    # AB组分列（优先级最高）
                    if re.search(r'[a-z0-9-]+[ab]\b|[ab]组分|[ab]剂|主剂|固化剂|[ab]组分包装', cell_text, re.IGNORECASE):
                        if col_idx not in value_cols:
                            value_cols.append(col_idx)
                    # 普通值列（包含数字、产品特征等，但排除参数名）
                    elif (re.search(r'\d|慢速|中速|快速|产品', cell_text) and
                          not any(keyword in cell_text for keyword in ['项目', 'parameter', '参数', '测试项目'])):
                        if col_idx not in value_cols:
                            value_cols.append(col_idx)

        # 如果没有找到参数列，假设第一列是参数列
        if param_col is None:
            param_col = 0

        # 如果没有找到值列，智能推断
        if not value_cols and table_data:
            # 排除参数列和明显的非值列
            for col_idx in range(len(table_data[0])):
                if col_idx == param_col:
                    continue

                # 检查这一列的内容特征
                col_content = [str(row[col_idx]).strip() if col_idx < len(row) else "" for row in table_data[:5]]

                # 如果列内容主要是单位、标准等，跳过
                unit_keywords = ['单位', 'unit', '标准', 'standard', '执行标准', '测试方法', '条件']
                if any(any(keyword in content.lower() for keyword in unit_keywords) for content in col_content):
                    continue

                # 检查是否包含实际数值
                has_values = any(self._looks_like_value(content) for content in col_content)
                if has_values:
                    value_cols.append(col_idx)

        # 对值列进行排序，优先选择包含更多数值的列
        if value_cols and table_data:
            def count_values_in_column(col_idx):
                count = 0
                for row in table_data[1:]:  # 跳过表头
                    if col_idx < len(row):
                        content = str(row[col_idx]).strip()
                        if self._looks_like_value(content):
                            count += 1
                return count

            value_cols.sort(key=count_values_in_column, reverse=True)

        return param_col, value_cols
    
    def _is_ab_table(self, table_data: List[List]) -> bool:
        """判断是否为AB组件表格

        基于第一性原则：只要表格包含外观/粘度/密度任意一个参数，就标记为AB表格
        """
        if not table_data or len(table_data) < 2:
            return False

        # AB核心参数：外观、粘度、密度
        ab_core_params = ['外观', '粘度', '密度']

        # 检查表格中是否包含AB核心参数
        for row in table_data[1:]:  # 跳过表头
            if row:
                param_name = str(row[0]).strip()
                if any(core_param == param_name for core_param in ab_core_params):
                    self.logger.debug(f"AB表格确认: 发现AB核心参数 '{param_name}'")
                    return True

        return False
    
    def _generate_ab_key(self, param_name: str, col_idx: int, table_data: List[List]) -> Optional[str]:
        """为AB组件生成参数键"""
        import re

        # 查找列标题确定是A还是B组分（只检查第一行表头）
        if table_data and col_idx < len(table_data[0]):
            header = str(table_data[0][col_idx]).strip()

            # 检查A组分标识
            a_patterns = [
                r'\ba组分\b', r'\ba剂\b', r'\b主剂\b',
                r'[a-z0-9-]+a\b',  # 如 109-17A, FM-3602-1A
                r'a组分包装',
            ]

            for pattern in a_patterns:
                if re.search(pattern, header, re.IGNORECASE):
                    self.logger.debug(f"A组分列识别: '{header}' -> {param_name}(A)")
                    return f"{param_name}(A)"

            # 检查B组分标识
            b_patterns = [
                r'\bb组分\b', r'\bb剂\b', r'\b固化剂\b',
                r'[a-z0-9-]+b\b',  # 如 109-17B, FM-3602-1B
                r'b组分包装',
            ]

            for pattern in b_patterns:
                if re.search(pattern, header, re.IGNORECASE):
                    self.logger.debug(f"B组分列识别: '{header}' -> {param_name}(B)")
                    return f"{param_name}(B)"

        # 如果不是AB列，返回原参数名
        return param_name
    
    def _extract_from_text(self, text: str) -> Dict[str, str]:
        """从文本中提取参数"""
        parameters = {}
        
        # 常见的参数模式
        patterns = [
            r'([^：:\n]+)[：:]\s*([^\n]+)',
            r'([^=\n]+)=\s*([^\n]+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for param_name, value in matches:
                param_name = param_name.strip()
                value = value.strip()
                
                if param_name and value and len(param_name) < 50 and len(value) < 200:
                    parameters[param_name] = value
        
        return parameters
    
    def _match_parameters(self, raw_parameters: Dict[str, str]) -> Dict[str, str]:
        """匹配参数到标准格式"""
        matched_parameters = {}

        for raw_param, value in raw_parameters.items():
            # 检查是否已经是AB组分格式
            if raw_param.endswith('(A)') or raw_param.endswith('(B)'):
                # 已经是AB格式，直接使用
                matched_parameters[raw_param] = value
                self.logger.debug(f"保留AB参数: {raw_param}")
                continue

            # 直接查找精确匹配
            cleaned_param = self._clean_text_for_matching(raw_param)

            standard_param = None

            # 1. 精确匹配
            if cleaned_param.lower() in self.alias_to_standard:
                standard_param = self.alias_to_standard[cleaned_param.lower()]
            else:
                # 2. 模糊匹配
                standard_param = self._fuzzy_match_parameter(raw_param)

            if standard_param:
                matched_parameters[standard_param] = value
                self.logger.debug(f"参数匹配: {raw_param} -> {standard_param}")
            else:
                # 保留原始参数名
                matched_parameters[raw_param] = value
                self.logger.debug(f"参数未匹配: {raw_param}")

        return matched_parameters
    
    def _fuzzy_match_parameter(self, raw_param: str) -> Optional[str]:
        """模糊匹配参数"""
        best_match = None
        best_score = 0
        threshold = 0.8
        
        cleaned_raw = self._clean_text_for_matching(raw_param)
        
        for standard_param, aliases in self.param_mapping.items():
            for alias in aliases:
                score = fuzz.ratio(cleaned_raw.lower(), alias.lower()) / 100.0
                if score > best_score and score >= threshold:
                    best_score = score
                    best_match = standard_param
        
        return best_match
    
    def _clean_text_for_matching(self, text: str) -> str:
        """清理文本用于匹配"""
        if not text:
            return ""
        
        # 移除括号内容
        cleaned = re.sub(r'\([^)]*\)', '', text)
        cleaned = re.sub(r'（[^）]*）', '', cleaned)
        
        # 移除数字和特殊字符
        cleaned = re.sub(r'[0-9]+', '', cleaned)
        cleaned = re.sub(r'[^\u4e00-\u9fff\w]', '', cleaned)
        
        return cleaned.strip()
    
    def _is_unit_or_header(self, text: str) -> bool:
        """判断是否为单位或表头"""
        text = text.strip().lower()

        # 常见单位
        units = ['mpa·s', 'pa·s', 'g/cm³', 'g/ml', '℃', '°c', 'min', 'h', 'hour', 'day', 'month', 'year']
        if any(unit in text for unit in units):
            return True

        # 表头关键词
        headers = ['测试项目', '项目', 'parameter', '参数', '单位', 'unit', '标准', 'standard']
        if text in headers:
            return True

        return False

    def _looks_like_value(self, text: str) -> bool:
        """判断文本是否看起来像数值"""
        if not text:
            return False

        text = text.strip()

        # 包含数字的内容
        import re
        if re.search(r'\d', text):
            return True

        # 特殊值标识
        special_values = ['透明', '黑色', '黄色', '白色', '无色', '液体', '固体', 'v-0', 'v0', 'v1', 'v2']
        if any(val in text.lower() for val in special_values):
            return True

        # 比较符号开头的值
        if re.match(r'^[><=≥≤]', text):
            return True

        # 排除明显的单位和标准
        exclude_patterns = [
            r'^(mpa·s|pa·s|g/cm|℃|°c|min|h|hour|day|month|year)$',
            r'^(gb/t|ul-94|shore|kv/mm)$',
            r'标准$|条件$|方法$'
        ]

        for pattern in exclude_patterns:
            if re.search(pattern, text.lower()):
                return False

        return False

    def _calculate_value_score(self, param_name: str, value: str) -> int:
        """计算值的匹配评分，用于选择最佳值"""
        if not value:
            return 0

        score = 0
        value_lower = value.lower()
        param_lower = param_name.lower()

        # 包含数字的内容得分高
        import re
        if re.search(r'\d', value):
            score += 10

        # 特殊参数的特殊处理
        if '阻燃' in param_lower:
            # 阻燃等级：V-0, V0 等比 UL-94 更重要
            if re.search(r'v-?[0-2]', value_lower):
                score += 20
            elif 'ul-94' in value_lower:
                score += 5

        if '外观' in param_lower:
            # 外观：颜色和状态描述比单位重要
            appearance_keywords = ['透明', '黑色', '黄色', '白色', '无色', '液体', '固体', '粘稠']
            if any(keyword in value_lower for keyword in appearance_keywords):
                score += 15

        # 比较符号开头的值
        if re.match(r'^[><=≥≤]', value):
            score += 8

        # 排除明显的单位和标准
        exclude_patterns = [
            r'^(mpa·s|pa·s|g/cm|℃|°c|min|h|hour|day|month|year)$',
            r'^(gb/t|ul-94|shore|kv/mm)$',
            r'标准$|条件$|方法$|目测$'
        ]

        for pattern in exclude_patterns:
            if re.search(pattern, value_lower):
                score -= 10

        return max(0, score)

    def _process_ab_components(self, parameters: Dict[str, str], product_name: str) -> Dict[str, str]:
        """处理AB组件逻辑"""
        processed = {}

        # 检测是否为AB组件产品
        is_ab_product = self._is_ab_product(parameters, product_name)

        for param_name, value in parameters.items():
            # 如果参数已经有AB标识，直接使用
            if param_name.endswith('(A)') or param_name.endswith('(B)'):
                processed[param_name] = value
                self.logger.debug(f"保留AB参数: {param_name} = {value}")
                continue

            # 检查是否需要AB处理（仅对标准参数）
            if is_ab_product and param_name in self.ab_parameters:
                # 尝试解析值中的AB信息
                ab_values = self._parse_ab_values(value)
                if ab_values:
                    if 'A' in ab_values:
                        processed[f"{param_name}(A)"] = ab_values['A']
                        self.logger.debug(f"解析AB参数A: {param_name}(A) = {ab_values['A']}")
                    if 'B' in ab_values:
                        processed[f"{param_name}(B)"] = ab_values['B']
                        self.logger.debug(f"解析AB参数B: {param_name}(B) = {ab_values['B']}")
                else:
                    # 无法解析，保持原样
                    processed[param_name] = value
                    self.logger.debug(f"AB参数无法解析，保持原样: {param_name} = {value}")
            else:
                # 非AB参数或非AB产品，直接使用
                processed[param_name] = value

        return processed
    
    def _is_ab_product(self, parameters: Dict[str, str], product_name: str) -> bool:
        """检测是否为AB组件产品"""
        # 检查产品名称
        ab_indicators = ["双组分", "双组份", "A/B", "A+B", "两组分", "两组份"]
        if any(indicator in product_name for indicator in ab_indicators):
            return True
        
        # 检查参数中是否有AB组件相关信息
        ab_keywords = ["A组分", "B组分", "A剂", "B剂", "主剂", "固化剂", "混合比例", "(A)", "(B)"]
        for param_name in parameters.keys():
            if any(keyword in param_name for keyword in ab_keywords):
                return True
        
        return False
    
    def _parse_ab_values(self, value: str) -> Optional[Dict[str, str]]:
        """解析值中的AB组分信息"""
        try:
            patterns = [
                r'A[组剂]?[分份]?[：:]\s*([^,，;；\n]+)[,，;；\s]*B[组剂]?[分份]?[：:]\s*([^,，;；\n]+)',
                r'主剂[：:]\s*([^,，;；\n]+)[,，;；\s]*固化剂[：:]\s*([^,，;；\n]+)',
                r'([^/]+)/([^/]+)',
            ]
            
            for pattern in patterns:
                match = re.search(pattern, value)
                if match:
                    return {
                        'A': match.group(1).strip(),
                        'B': match.group(2).strip()
                    }
            
            return None
            
        except Exception as e:
            self.logger.debug(f"AB值解析失败: {e}")
            return None
    
    def scan_folder(self, folder_path: str) -> List[str]:
        """扫描文件夹中的支持文件"""
        supported_files = []
        
        if not os.path.exists(folder_path):
            self.logger.error(f"文件夹不存在: {folder_path}")
            return supported_files
        
        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                if filename.lower().endswith(('.pdf', '.docx', '.doc')):
                    supported_files.append(file_path)
        
        self.logger.info(f"扫描到 {len(supported_files)} 个支持的文件")
        return supported_files
