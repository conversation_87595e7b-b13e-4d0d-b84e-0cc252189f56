#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一翻译系统主入口
基于第一性原则重构的翻译系统
"""

import sys
import os
from pathlib import Path
from typing import Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.utils import get_logger, get_config_manager
from src.translation.unified_translator import UnifiedTranslator
from src.translation.unified_document_processor import UnifiedDocumentProcessor

logger = get_logger(__name__)


class TranslationSystem:
    """
    统一翻译系统
    
    提供完整的翻译功能：
    1. 文本翻译
    2. 文档翻译
    3. 批量处理
    4. 学习建议
    5. 质量分析
    """
    
    def __init__(self, cloud_only: bool = False):
        self.cloud_only = cloud_only
        self.logger = get_logger(__name__)
        
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        
        # 初始化翻译引擎
        self.translator = UnifiedTranslator({'cloud_only': cloud_only})
        
        # 初始化文档处理器
        self.document_processor = UnifiedDocumentProcessor(cloud_only)
        
        mode = "仅云端模式" if cloud_only else "混合模式"
        self.logger.info(f"统一翻译系统初始化完成，运行模式: {mode}")
    
    def translate_text(self, text: str, show_details: bool = False) -> str:
        """
        翻译文本
        
        Args:
            text: 要翻译的文本
            show_details: 是否显示详细信息
            
        Returns:
            str: 翻译结果
        """
        if not text or not text.strip():
            return ""
        
        try:
            # 执行翻译
            context = self.translator.translate_text(text)
            
            # 格式化输出
            if show_details:
                return self.translator.format_output(context, 'detailed')
            else:
                return self.translator.format_output(context, 'text')
                
        except Exception as e:
            self.logger.error(f"文本翻译失败: {e}")
            return f"翻译失败: {str(e)}"
    
    def translate_file(self, input_file: str, output_file: str = None, 
                      generate_report: bool = True) -> bool:
        """
        翻译单个文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径（可选）
            generate_report: 是否生成学习报告
            
        Returns:
            bool: 翻译是否成功
        """
        try:
            result = self.document_processor.process_single_file(
                input_file, output_file, generate_report
            )
            
            if result.success:
                self.logger.info(f"文件翻译成功: {result.output_file}")
                if result.learning_suggestions_count > 0:
                    self.logger.info(f"生成了 {result.learning_suggestions_count} 个学习建议")
            else:
                self.logger.error(f"文件翻译失败: {result.error_message}")
            
            return result.success
            
        except Exception as e:
            self.logger.error(f"文件翻译过程出错: {e}")
            return False
    
    def translate_folder(self, input_folder: str, output_folder: str = None, 
                        generate_reports: bool = True) -> dict:
        """
        批量翻译文件夹
        
        Args:
            input_folder: 输入文件夹路径
            output_folder: 输出文件夹路径（可选）
            generate_reports: 是否生成学习报告
            
        Returns:
            dict: 批量处理结果
        """
        try:
            results = self.document_processor.process_folder(
                input_folder, output_folder, generate_reports
            )
            
            if results['success']:
                self.logger.info(f"批量翻译完成: {results['success_files']}/{results['total_files']} 成功")
                if results['failed_files'] > 0:
                    self.logger.warning(f"有 {results['failed_files']} 个文件翻译失败")
            else:
                self.logger.error(f"批量翻译失败: {results.get('error', '未知错误')}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"批量翻译过程出错: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_translation_analysis(self, text: str) -> str:
        """
        获取翻译分析报告
        
        Args:
            text: 要分析的文本
            
        Returns:
            str: 分析报告
        """
        try:
            context = self.translator.translate_text(text)
            return self.translator.get_learning_report(context, 'text')
            
        except Exception as e:
            self.logger.error(f"翻译分析失败: {e}")
            return f"分析失败: {str(e)}"
    
    def get_system_status(self) -> dict:
        """获取系统状态"""
        status = {
            'mode': '仅云端模式' if self.cloud_only else '混合模式',
            'helsinki_available': hasattr(self.translator, 'helsinki_available') and self.translator.helsinki_available,
            'tencent_available': hasattr(self.translator, 'tencent_available') and self.translator.tencent_available,
            'supported_formats': self.document_processor.get_supported_formats()
        }
        
        return status


def interactive_mode():
    """交互模式"""
    print("=" * 60)
    print("🚀 统一翻译系统 - 交互模式")
    print("=" * 60)
    
    # 检查环境并选择模式
    cloud_only = False
    try:
        # 尝试导入Helsinki翻译器
        from .translator import HelsinkiTranslator
        print("✅ 检测到本地翻译环境")
    except Exception:
        print("⚠️  本地翻译环境不可用，将使用仅云端模式")
        cloud_only = True
    
    # 初始化系统
    system = TranslationSystem(cloud_only=cloud_only)
    status = system.get_system_status()
    
    print(f"\n📊 系统状态:")
    print(f"  运行模式: {status['mode']}")
    print(f"  本地翻译: {'可用' if status['helsinki_available'] else '不可用'}")
    print(f"  云端翻译: {'可用' if status['tencent_available'] else '不可用'}")
    print(f"  支持格式: {', '.join(status['supported_formats'])}")
    
    while True:
        print("\n" + "=" * 60)
        print("请选择功能:")
        print("1. 📝 文本翻译")
        print("2. 📄 文件翻译")
        print("3. 📁 文件夹批量翻译")
        print("4. 📊 翻译分析")
        print("5. ❌ 退出")
        print("=" * 60)
        
        try:
            choice = input("请输入选择 (1-5): ").strip()
            
            if choice == '1':
                text_translation_mode(system)
            elif choice == '2':
                file_translation_mode(system)
            elif choice == '3':
                folder_translation_mode(system)
            elif choice == '4':
                analysis_mode(system)
            elif choice == '5':
                print("👋 感谢使用统一翻译系统！")
                break
            else:
                print("❌ 无效选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 操作出错: {e}")


def text_translation_mode(system: TranslationSystem):
    """文本翻译模式"""
    print("\n📝 文本翻译模式")
    print("输入要翻译的文本（输入 'quit' 退出）:")
    
    while True:
        try:
            text = input("\n> ").strip()
            
            if text.lower() == 'quit':
                break
            
            if not text:
                print("请输入要翻译的文本")
                continue
            
            print("\n翻译中...")
            result = system.translate_text(text, show_details=True)
            print(f"\n{result}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 翻译出错: {e}")


def file_translation_mode(system: TranslationSystem):
    """文件翻译模式"""
    print("\n📄 文件翻译模式")
    
    input_file = input("请输入文件路径: ").strip()
    if not input_file:
        print("❌ 文件路径不能为空")
        return
    
    output_file = input("请输入输出文件路径（回车使用默认）: ").strip()
    if not output_file:
        output_file = None
    
    generate_report = input("是否生成学习报告？(y/n，默认y): ").strip().lower()
    generate_report = generate_report != 'n'
    
    print("\n翻译中...")
    success = system.translate_file(input_file, output_file, generate_report)
    
    if success:
        print("✅ 文件翻译完成！")
    else:
        print("❌ 文件翻译失败")


def folder_translation_mode(system: TranslationSystem):
    """文件夹翻译模式"""
    print("\n📁 文件夹批量翻译模式")
    
    input_folder = input("请输入文件夹路径: ").strip()
    if not input_folder:
        print("❌ 文件夹路径不能为空")
        return
    
    output_folder = input("请输入输出文件夹路径（回车使用默认）: ").strip()
    if not output_folder:
        output_folder = None
    
    generate_reports = input("是否生成学习报告？(y/n，默认y): ").strip().lower()
    generate_reports = generate_reports != 'n'
    
    print("\n批量翻译中...")
    results = system.translate_folder(input_folder, output_folder, generate_reports)
    
    if results['success']:
        print(f"✅ 批量翻译完成！成功: {results['success_files']}, 失败: {results['failed_files']}")
    else:
        print(f"❌ 批量翻译失败: {results.get('error', '未知错误')}")


def analysis_mode(system: TranslationSystem):
    """翻译分析模式"""
    print("\n📊 翻译分析模式")
    
    text = input("请输入要分析的文本: ").strip()
    if not text:
        print("❌ 文本不能为空")
        return
    
    print("\n分析中...")
    analysis = system.get_translation_analysis(text)
    print(f"\n{analysis}")


def main():
    """主函数"""
    try:
        interactive_mode()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
