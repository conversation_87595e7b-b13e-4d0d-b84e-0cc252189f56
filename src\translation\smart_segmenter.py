#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文本分段器
基于第一性原则重构的分段系统，支持逐步细分重试机制
"""

import re
from typing import List, Dict, Tuple
from ..utils import get_logger

logger = get_logger(__name__)


class SmartSegmenter:
    """
    智能文本分段器
    
    实现分层分段策略：
    1. 句子级分割（按句号、感叹号、问号）
    2. 子句级分割（按逗号、分号）
    3. 短语级分割（按空格、括号）
    4. 词汇级分割（按字符）
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 分段策略配置
        self.strategies = [
            ('sentence', self._split_by_sentences, 0),      # 句子分割，无最小长度限制
            ('clause', self._split_by_clauses, 8),          # 子句分割，最小8字符
            ('phrase', self._split_by_phrases, 15),         # 短语分割，最小15字符
            ('word', self._split_by_words, 25),             # 词汇分割，最小25字符
        ]
        
        # 保护模式：不分割的内容
        self.protected_patterns = [
            r'\d+(?:\.\d+)?[A-Za-z·℃%]+',  # 数值+单位
            r'FM-[A-Z0-9-]+',              # 产品型号
            r'\([^)]{1,20}\)',             # 短括号内容
            r'[A-Za-z]+\d+[A-Za-z]*',      # 字母数字组合
        ]
    
    def segment_text(self, text: str, max_length: int = 20) -> List[str]:
        """
        智能分段主入口
        
        Args:
            text: 要分段的文本
            max_length: 目标最大长度
            
        Returns:
            List[str]: 分段结果
        """
        if not text or not text.strip():
            return []
        
        text = text.strip()
        
        # 保护特殊内容
        protected_text, placeholder_map = self._protect_special_content(text)
        
        # 执行分层分段
        segments = self._execute_layered_segmentation(protected_text, max_length)
        
        # 恢复保护的内容
        segments = self._restore_protected_content(segments, placeholder_map)
        
        # 清理和过滤
        segments = self._clean_and_filter(segments)
        
        self.logger.debug(f"文本分段完成: {len(text)} 字符 -> {len(segments)} 个片段")
        return segments
    
    def fine_segment_text(self, text: str, target_length: int = 10) -> List[str]:
        """
        精细分段（用于重试机制）
        
        Args:
            text: 要分段的文本
            target_length: 目标长度（更小）
            
        Returns:
            List[str]: 精细分段结果
        """
        if len(text) <= target_length:
            return [text]
        
        segments = []
        
        # 策略1: 按逗号分割
        comma_parts = text.split('，')
        if len(comma_parts) > 1:
            for part in comma_parts:
                if part.strip():
                    segments.extend(self._further_split(part.strip(), target_length))
        else:
            # 策略2: 按空格分割
            space_parts = text.split()
            if len(space_parts) > 1:
                for part in space_parts:
                    if part.strip():
                        segments.extend(self._further_split(part.strip(), target_length))
            else:
                # 策略3: 按字符分组
                segments = self._split_by_characters(text, target_length)
        
        return [s for s in segments if s.strip()]
    
    def _protect_special_content(self, text: str) -> Tuple[str, Dict[str, str]]:
        """保护特殊内容不被分割"""
        placeholder_map = {}
        protected_text = text
        
        for i, pattern in enumerate(self.protected_patterns):
            matches = re.finditer(pattern, protected_text)
            for j, match in enumerate(matches):
                placeholder = f"__PROTECTED_{i}_{j}__"
                placeholder_map[placeholder] = match.group()
                protected_text = protected_text.replace(match.group(), placeholder, 1)
        
        return protected_text, placeholder_map
    
    def _restore_protected_content(self, segments: List[str], placeholder_map: Dict[str, str]) -> List[str]:
        """恢复保护的内容"""
        restored_segments = []
        
        for segment in segments:
            restored_segment = segment
            for placeholder, original in placeholder_map.items():
                restored_segment = restored_segment.replace(placeholder, original)
            restored_segments.append(restored_segment)
        
        return restored_segments
    
    def _execute_layered_segmentation(self, text: str, max_length: int) -> List[str]:
        """执行分层分段"""
        segments = [text]
        
        for strategy_name, split_func, min_length in self.strategies:
            new_segments = []
            
            for segment in segments:
                if len(segment) > max_length and len(segment) > min_length:
                    # 需要进一步分割
                    sub_segments = split_func(segment)
                    new_segments.extend(sub_segments)
                else:
                    # 保持原样
                    new_segments.append(segment)
            
            segments = new_segments
            
            # 检查是否达到目标
            max_segment_length = max(len(s) for s in segments) if segments else 0
            if max_segment_length <= max_length:
                break
        
        return segments
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """按句子分割"""
        # 句子结束标点
        sentence_endings = ['.', '!', '?', '。', '！', '？', ';', '；']
        
        segments = []
        current_segment = ""
        
        for char in text:
            current_segment += char
            if char in sentence_endings:
                if current_segment.strip():
                    segments.append(current_segment.strip())
                current_segment = ""
        
        # 添加剩余部分
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return segments if segments else [text]
    
    def _split_by_clauses(self, text: str) -> List[str]:
        """按子句分割"""
        # 子句分隔符
        clause_separators = ['，', ',', '、', '；', ';']
        
        segments = []
        current_segment = ""
        
        for char in text:
            current_segment += char
            if char in clause_separators:
                if current_segment.strip():
                    segments.append(current_segment.strip())
                current_segment = ""
        
        # 添加剩余部分
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return segments if segments else [text]
    
    def _split_by_phrases(self, text: str) -> List[str]:
        """按短语分割"""
        # 短语分隔符
        phrase_separators = [' ', '　', '(', ')', '（', '）', '[', ']', '【', '】']
        
        segments = []
        current_segment = ""
        
        for char in text:
            if char in phrase_separators:
                if current_segment.strip():
                    segments.append(current_segment.strip())
                current_segment = ""
            else:
                current_segment += char
        
        # 添加剩余部分
        if current_segment.strip():
            segments.append(current_segment.strip())
        
        return segments if segments else [text]
    
    def _split_by_words(self, text: str) -> List[str]:
        """按词汇分割"""
        # 使用简单的字符分组
        segments = []
        
        # 按3-5个字符分组
        group_size = min(5, max(3, len(text) // 4))
        
        for i in range(0, len(text), group_size):
            segment = text[i:i + group_size]
            if segment.strip():
                segments.append(segment.strip())
        
        return segments if segments else [text]
    
    def _further_split(self, text: str, target_length: int) -> List[str]:
        """进一步分割文本"""
        if len(text) <= target_length:
            return [text]
        
        # 尝试按标点分割
        for separator in ['，', ',', ' ', '　']:
            if separator in text:
                parts = text.split(separator)
                if len(parts) > 1:
                    result = []
                    for part in parts:
                        if part.strip():
                            result.extend(self._further_split(part.strip(), target_length))
                    return result
        
        # 按字符分组
        return self._split_by_characters(text, target_length)
    
    def _split_by_characters(self, text: str, target_length: int) -> List[str]:
        """按字符分组"""
        segments = []
        
        for i in range(0, len(text), target_length):
            segment = text[i:i + target_length]
            if segment.strip():
                segments.append(segment.strip())
        
        return segments
    
    def _clean_and_filter(self, segments: List[str]) -> List[str]:
        """清理和过滤分段结果"""
        cleaned_segments = []
        
        for segment in segments:
            # 清理空白字符
            cleaned = segment.strip()
            
            # 过滤太短的片段（除非是数字或英文）
            if len(cleaned) >= 1:
                # 保留数字、英文、或长度>=2的中文
                if (re.match(r'^[\d\w]+$', cleaned) or 
                    len(cleaned) >= 2 or 
                    re.match(r'^[A-Za-z·℃%]+$', cleaned)):
                    cleaned_segments.append(cleaned)
        
        return cleaned_segments
    
    def get_segmentation_quality(self, segments: List[str], target_length: int = 20) -> Dict[str, float]:
        """评估分段质量"""
        if not segments:
            return {'quality_score': 0.0, 'avg_length': 0.0, 'ideal_ratio': 0.0}
        
        # 计算平均长度
        avg_length = sum(len(s) for s in segments) / len(segments)
        
        # 计算理想长度片段比例
        ideal_segments = [s for s in segments if 3 <= len(s) <= target_length]
        ideal_ratio = len(ideal_segments) / len(segments)
        
        # 计算质量分数
        length_score = 1.0 - abs(avg_length - target_length) / target_length
        length_score = max(0.0, min(1.0, length_score))
        
        quality_score = (ideal_ratio * 0.7) + (length_score * 0.3)
        
        return {
            'quality_score': quality_score,
            'avg_length': avg_length,
            'ideal_ratio': ideal_ratio,
            'total_segments': len(segments)
        }
