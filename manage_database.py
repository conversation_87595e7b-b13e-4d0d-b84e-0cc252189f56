#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理工具
提供数据库的维护和管理功能
"""

import sys
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def show_statistics(db):
    """显示数据库统计信息"""
    stats = db.get_statistics()
    print("\n📊 数据库统计信息:")
    print(f"  术语总数: {stats['total_terms']}")
    print(f"  智能模式: {stats['total_patterns']}")
    print(f"  翻译记录: {stats['total_translations']}")
    print(f"  待学习记录: {stats['pending_learning']}")

def cleanup_duplicates(db):
    """清理重复数据"""
    print("🧹 清理重复数据...")
    removed = db.cleanup_duplicates()
    print(f"✅ 清理了 {removed} 个重复条目")

def export_data(db, output_dir):
    """导出数据"""
    from src.database.migration import DataMigration
    migration = DataMigration(db)
    migration.export_to_files(output_dir)
    print(f"✅ 数据导出到: {output_dir}")

def search_terms(db, keyword):
    """搜索术语"""
    terms = db.get_all_terms()
    matches = []
    
    for chinese, english in terms.items():
        if keyword.lower() in chinese.lower() or keyword.lower() in english.lower():
            matches.append((chinese, english))
    
    if matches:
        print(f"\n🔍 找到 {len(matches)} 个匹配的术语:")
        for chinese, english in matches[:20]:  # 限制显示前20个
            print(f"  '{chinese}' → '{english}'")
        if len(matches) > 20:
            print(f"  ... 还有 {len(matches) - 20} 个结果")
    else:
        print(f"❌ 未找到包含 '{keyword}' 的术语")

def add_term(db, chinese, english, term_type="basic"):
    """添加新术语"""
    from src.database.models import TranslationTerm, TermType, TranslationSource
    
    try:
        term_type_enum = TermType(term_type)
    except ValueError:
        print(f"❌ 无效的术语类型: {term_type}")
        print(f"可用类型: {[t.value for t in TermType]}")
        return
    
    term = TranslationTerm(
        chinese=chinese,
        english=english,
        term_type=term_type_enum,
        source=TranslationSource.MANUAL,
        confidence=1.0,
        context="手动添加"
    )
    
    term_id = db.add_term(term)
    if term_id:
        print(f"✅ 添加术语成功: '{chinese}' → '{english}'")
    else:
        print(f"❌ 添加术语失败")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="翻译数据库管理工具")
    parser.add_argument("command", choices=[
        "stats", "cleanup", "export", "search", "add"
    ], help="要执行的命令")
    
    parser.add_argument("--keyword", help="搜索关键词")
    parser.add_argument("--chinese", help="中文术语")
    parser.add_argument("--english", help="英文翻译")
    parser.add_argument("--type", default="basic", help="术语类型")
    parser.add_argument("--output", default="data/export", help="导出目录")
    
    args = parser.parse_args()
    
    try:
        from src.database import TranslationDatabase
        
        db = TranslationDatabase()
        
        if args.command == "stats":
            show_statistics(db)
            
        elif args.command == "cleanup":
            cleanup_duplicates(db)
            
        elif args.command == "export":
            export_data(db, args.output)
            
        elif args.command == "search":
            if not args.keyword:
                print("❌ 请提供搜索关键词: --keyword")
                return
            search_terms(db, args.keyword)
            
        elif args.command == "add":
            if not args.chinese or not args.english:
                print("❌ 请提供中文和英文: --chinese --english")
                return
            add_term(db, args.chinese, args.english, args.type)
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
