#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能汇总表生成器 v3.0 - 优化版
基于配置驱动的简化架构
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger, get_config_manager, create_progress_tracker
from src.validation import DataValidator
from src.translation import UnifiedDocumentProcessor

# 导入新的简化汇总表生成器模块
from src.summary_table import ConfigBasedExtractor, SummaryTableGenerator, SummaryTableConfig

logger = get_logger(__name__)


class OptimizedTableGenerator:
    """优化的智能汇总表生成器 v3.0
    
    核心改进：
    1. 基于配置驱动的数据提取，直接使用config.json的param_mapping
    2. 简化的数据流：文档 -> 内容提取 -> 配置匹配 -> 标准化输出
    3. 统一的AB组件处理逻辑
    4. 保留翻译功能的独立性
    """
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.validator = DataValidator()
        
        # 初始化翻译功能（可选）
        self._init_translation()
        
        # 使用新的配置驱动架构
        self.summary_config = SummaryTableConfig()
        self.extractor = ConfigBasedExtractor(self.summary_config)
        self.generator = SummaryTableGenerator(self.summary_config)
        
        logger.info("优化的智能汇总表生成器 v3.0 初始化完成")
    
    def _init_translation(self):
        """初始化翻译功能"""
        try:
            self.document_translator = UnifiedDocumentProcessor()
            logger.info("翻译功能初始化成功")
        except Exception as e:
            logger.warning(f"翻译功能初始化失败: {e}")
            self.document_translator = None
    
    def extract_to_excel(self, folder_path: str = None, output_file: str = None):
        """提取TDS/DOCS文件内容到Excel
        
        这是核心功能1：从技术参数文档提取数据生成Excel汇总表
        """
        logger.info("=" * 60)
        logger.info("📊 开始提取TDS/DOCS文件内容到Excel")
        logger.info("=" * 60)
        
        # 获取配置
        if folder_path is None:
            folder_path = self.config_manager.get_setting("input_folder", "PDF")
        if output_file is None:
            output_file = self.config_manager.get_setting("output_file", "智能汇总表.xlsx")
        
        logger.info(f"输入文件夹: {folder_path}")
        logger.info(f"输出文件: {output_file}")
        
        # 扫描支持的文件
        file_paths = self.extractor.scan_folder(folder_path)
        if not file_paths:
            logger.error("没有找到支持的文档文件")
            return False
        
        logger.info(f"找到 {len(file_paths)} 个文档文件")
        
        # 创建进度跟踪器
        progress = create_progress_tracker(len(file_paths), "提取技术参数")
        
        # 提取数据
        all_data = []
        validation_results = []
        
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            
            try:
                # 使用新的配置驱动提取器
                data = self.extractor.extract_from_file(file_path)
                
                if data and data.get('parameters'):
                    all_data.append(data)
                    
                    # 数据验证
                    validation_result = self.validator.validate_product_data(
                        filename, 
                        data.get('product_name', ''), 
                        data.get('parameters', {})
                    )
                    validation_results.append(validation_result)
                    
                    # 记录成功
                    logger.log_file_processing(filename, "success")
                    progress.update(filename, "success", 
                                  f"参数: {len(data['parameters'])}, 质量: {validation_result.quality_score:.1f}")
                else:
                    logger.warning(f"文件数据提取失败: {filename}")
                    progress.update(filename, "error", "数据提取失败")
                    
            except Exception as e:
                logger.error(f"处理文件失败 {filename}: {e}")
                progress.update(filename, "error", str(e))
                continue
        
        progress.finish()
        
        if not all_data:
            logger.error("没有成功提取到任何数据")
            return False
        
        # 生成Excel汇总表
        logger.info(f"开始生成Excel汇总表，包含 {len(all_data)} 个产品...")
        success = self.generator.generate_summary_table(all_data, output_file)
        
        if success:
            # 生成验证报告
            self._generate_validation_report(validation_results)
            logger.info("✅ Excel汇总表生成完成！")
            logger.info(f"📁 输出文件: {output_file}")
            return True
        else:
            logger.error("❌ Excel汇总表生成失败")
            return False
    
    def translate_documents(self, input_folder: str = None, output_folder: str = None):
        """翻译文档功能"""
        if not self.document_translator:
            logger.error("翻译功能未初始化，请检查配置")
            return False
        
        logger.info("=" * 60)
        logger.info("🌐 开始文档翻译")
        logger.info("=" * 60)
        
        try:
            results = self.document_translator.process_folder(input_folder, output_folder)
            
            if results['total_files'] > 0:
                logger.info("✅ 文档翻译完成！")
                logger.info(f"📁 输出目录: {output_folder or self.config_manager.get_setting('translation_output_folder', 'PDF_EN')}")
                return True
            else:
                logger.warning("没有找到可翻译的文档文件")
                return False
                
        except Exception as e:
            logger.error(f"文档翻译失败: {e}")
            return False
    
    def _generate_validation_report(self, validation_results):
        """生成数据质量验证报告"""
        if not validation_results:
            return
        
        summary = self.validator.generate_validation_summary(validation_results)
        
        logger.info("=" * 60)
        logger.info("📋 数据质量报告")
        logger.info("=" * 60)
        logger.info(f"总文件数: {summary['total_files']}")
        logger.info(f"验证通过: {summary['valid_files']}")
        logger.info(f"验证失败: {summary['invalid_files']}")
        logger.info(f"验证通过率: {summary['validation_rate']:.1f}%")
        logger.info(f"平均质量评分: {summary['average_quality_score']:.1f}")
        logger.info(f"平均完成率: {summary['average_completion_rate']:.1f}%")
        
        if summary['common_missing_params']:
            logger.info("常见缺失参数:")
            for param, count in summary['common_missing_params']:
                logger.info(f"  • {param}: {count}次")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    try:
        print("=" * 60)
        print("🚀 智能汇总表生成器 v3.0 - 优化版")
        print("=" * 60)
        print("基于配置驱动的简化架构，提供以下功能：")
        print("1. 📊 提取TDS/DOCS文件内容到Excel - 核心数据提取功能")
        print("2. 🌐 文档翻译 - 将PDF/Word文档翻译为英文版本")
        print("=" * 60)

        while True:
            try:
                choice = input("请选择功能 (1 或 2): ").strip()
                if choice in ['1', '2']:
                    break
                else:
                    print("❌ 无效选择，请输入 1 或 2")
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                return

        generator = OptimizedTableGenerator()

        if choice == '1':
            print("\n📊 启动数据提取功能...")
            success = generator.extract_to_excel()
            if success:
                print("\n🎉 数据提取完成！请查看生成的Excel文件。")
            else:
                print("\n❌ 数据提取失败，请检查日志。")
                
        elif choice == '2':
            print("\n🌐 启动文档翻译功能...")
            success = generator.translate_documents()
            if success:
                print("\n🎉 文档翻译完成！请查看输出目录。")
            else:
                print("\n❌ 文档翻译失败，请检查配置和日志。")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
