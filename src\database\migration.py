#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移工具
将现有的字典数据迁移到数据库
"""

import logging
from typing import List, Tuple

from .translation_db import TranslationDatabase
from .models import TranslationTerm, SmartPattern, TermType, TranslationSource

class DataMigration:
    """数据迁移类"""
    
    def __init__(self, db: TranslationDatabase):
        self.db = db
        self.logger = logging.getLogger(__name__)
    
    def migrate_from_dictionary_data(self):
        """从dictionary_data.py迁移数据"""
        try:
            from ..translation.dictionary_data import TECHNICAL_DICTIONARY
            from ..translation.smart_patterns import SMART_PATTERNS
            
            self.logger.info("开始迁移词典数据...")
            
            # 迁移基础词典
            migrated_terms = 0
            for chinese, english in TECHNICAL_DICTIONARY.items():
                term = TranslationTerm(
                    chinese=chinese,
                    english=english,
                    term_type=self._classify_term_type(chinese, english),
                    source=TranslationSource.INITIAL,
                    confidence=1.0,
                    context="从dictionary_data.py迁移"
                )
                
                if self.db.add_term(term):
                    migrated_terms += 1
            
            self.logger.info(f"迁移了 {migrated_terms} 个术语")
            
            # 迁移智能模式
            migrated_patterns = 0
            for i, (pattern, replacement) in enumerate(SMART_PATTERNS):
                smart_pattern = SmartPattern(
                    pattern=pattern,
                    replacement=replacement,
                    description=f"智能模式 {i+1}",
                    priority=i * 10,  # 保持原有顺序
                    source=TranslationSource.INITIAL
                )
                
                if self.db.add_smart_pattern(smart_pattern):
                    migrated_patterns += 1
            
            self.logger.info(f"迁移了 {migrated_patterns} 个智能模式")
            
            # 清理重复数据
            removed = self.db.cleanup_duplicates()
            if removed > 0:
                self.logger.info(f"清理了 {removed} 个重复条目")
            
            return True
            
        except ImportError as e:
            self.logger.error(f"导入数据失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"迁移数据失败: {e}")
            return False
    
    def _classify_term_type(self, chinese: str, english: str) -> TermType:
        """分类术语类型"""
        # 基于中文内容判断术语类型
        if any(keyword in chinese for keyword in ["技术参数表", "数据表"]):
            return TermType.TECHNICAL
        elif any(keyword in chinese for keyword in ["聚氨酯", "密封胶", "灌封胶"]):
            return TermType.COMPOUND
        elif any(keyword in chinese for keyword in ["工艺", "流程", "步骤"]):
            return TermType.PROCESS_FLOW
        elif any(keyword in chinese for keyword in ["性能", "测试", "标准"]):
            return TermType.TECHNICAL
        elif any(keyword in chinese for keyword in ["℃", "%", "MPa", "kg"]):
            return TermType.UNIT_PATTERN
        elif len(chinese) > 50:  # 长文本可能是产品描述
            return TermType.PRODUCT_DESC
        else:
            return TermType.BASIC
    
    def export_to_files(self, output_dir: str = "data/export"):
        """导出数据库内容到文件（备份用）"""
        from pathlib import Path
        import json
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 导出术语
        terms_dict = self.db.get_all_terms()
        with open(output_path / "terms.json", "w", encoding="utf-8") as f:
            json.dump(terms_dict, f, ensure_ascii=False, indent=2)
        
        # 导出智能模式
        patterns = self.db.get_smart_patterns()
        with open(output_path / "patterns.json", "w", encoding="utf-8") as f:
            json.dump(patterns, f, ensure_ascii=False, indent=2)
        
        # 导出统计信息
        stats = self.db.get_statistics()
        with open(output_path / "statistics.json", "w", encoding="utf-8") as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据导出到: {output_path}")
    
    def validate_migration(self) -> bool:
        """验证迁移结果"""
        try:
            from ..translation.dictionary_data import TECHNICAL_DICTIONARY
            from ..translation.smart_patterns import SMART_PATTERNS
            
            # 检查术语数量
            db_terms = self.db.get_all_terms()
            original_count = len(TECHNICAL_DICTIONARY)
            db_count = len(db_terms)
            
            self.logger.info(f"原始术语数量: {original_count}")
            self.logger.info(f"数据库术语数量: {db_count}")
            
            # 检查智能模式数量
            db_patterns = self.db.get_smart_patterns()
            original_pattern_count = len(SMART_PATTERNS)
            db_pattern_count = len(db_patterns)
            
            self.logger.info(f"原始智能模式数量: {original_pattern_count}")
            self.logger.info(f"数据库智能模式数量: {db_pattern_count}")
            
            # 检查关键术语是否存在
            key_terms = ["双组份", "聚氨酯密封胶", "座便器", "燃气点火器"]
            missing_terms = []
            for term in key_terms:
                if term not in db_terms:
                    missing_terms.append(term)
            
            if missing_terms:
                self.logger.warning(f"缺失关键术语: {missing_terms}")
                return False
            
            self.logger.info("迁移验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"验证失败: {e}")
            return False
