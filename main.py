#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能汇总表生成器 v2.0
主程序入口
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger, get_config_manager, create_progress_tracker
from src.validation import DataValidator
from src.matching import get_fuzzy_matcher
from src.translation import UnifiedDocumentProcessor

# 导入新的汇总表生成器模块
from src.summary_table import TechnicalDataExtractor, SummaryTableGenerator, SummaryTableConfig

logger = get_logger(__name__)

class SmartTableGeneratorV2:
    """智能汇总表生成器 v2.0"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.validator = DataValidator()
        self.fuzzy_matcher = get_fuzzy_matcher()
        # 智能选择翻译模式
        try:
            # 首先尝试混合模式（本地+云端）
            self.document_translator = UnifiedDocumentProcessor()
            logger.info("使用混合翻译模式（本地+云端）")
        except Exception as e:
            logger.warning(f"混合模式初始化失败: {e}")
            try:
                # 回退到仅云端模式
                self.document_translator = UnifiedDocumentProcessor(cloud_only=True)
                logger.info("使用仅云端翻译模式")
            except Exception as e2:
                logger.error(f"所有翻译模式都失败: {e2}")
                logger.error("请检查腾讯云API配置或网络连接")
                raise

        # 使用新的汇总表生成器模块
        self.summary_config = SummaryTableConfig()
        self.extractor = TechnicalDataExtractor(self.summary_config)
        self.generator = SummaryTableGenerator(self.summary_config)

        logger.info("智能汇总表生成器 v2.0 初始化完成")
    
    def process_folder(self, folder_path: str = None, output_file: str = None):
        """处理文件夹"""
        # 获取配置
        if folder_path is None:
            folder_path = self.config_manager.get_setting("input_folder", "PDF")
        if output_file is None:
            output_file = self.config_manager.get_setting("output_file", "智能汇总表.xlsx")
        
        logger.info(f"开始处理文件夹: {folder_path}")
        logger.info(f"输出文件: {output_file}")
        
        # 扫描文件
        file_paths = self.extractor.scan_folder(folder_path)
        if not file_paths:
            logger.error("没有找到支持的 文件")
            return
        
        # 创建进度跟踪器
        progress = create_progress_tracker(len(file_paths), "处理技术参数文档")
        
        # 提取数据
        all_data = []
        validation_results = []
        
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            
            try:
                # 提取数据
                data = self._extract_file_data(file_path)
                if data:
                    all_data.append(data)
                    
                    # 数据验证
                    validation_result = self.validator.validate_product_data(
                        filename, data['product_name'], data['parameters']
                    )
                    validation_results.append(validation_result)
                    
                    # 记录日志
                    logger.log_file_processing(filename, "success")
                    logger.log_data_quality(
                        filename, 
                        validation_result.missing_params, 
                        validation_result.total_params
                    )
                    
                    progress.update(filename, "success", f"质量评分: {validation_result.quality_score:.1f}")
                else:
                    logger.log_file_processing(filename, "error", "数据提取失败")
                    progress.update(filename, "error", "数据提取失败")
                    
            except Exception as e:
                logger.log_file_processing(filename, "error", str(e))
                progress.update(filename, "error", str(e))
                continue
        
        progress.finish()
        
        if not all_data:
            logger.error("没有成功提取到任何数据")
            return
        
        # 生成汇总表
        logger.info("开始生成汇总表...")
        self.generator.generate_summary_table(all_data, output_file)
        
        # 生成验证报告
        self._generate_validation_report(validation_results)
        
        logger.info("处理完成！")

    def translate_documents(self, input_folder: str = None, output_folder: str = None):
        """翻译文档模式"""
        logger.info("=" * 60)
        logger.info("🌐 文档翻译模式")
        logger.info("=" * 60)

        try:
            # 执行翻译
            results = self.document_translator.process_folder(input_folder, output_folder)

            # 显示结果
            if results['total_files'] > 0:
                logger.info("翻译任务完成！")
                logger.info(f"📁 输出目录: {output_folder or self.config_manager.get_setting('translation_output_folder', 'PDF_EN')}")
            else:
                logger.warning("没有找到可翻译的文档文件")

        except Exception as e:
            logger.error(f"文档翻译过程中出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def _extract_file_data(self, file_path: str):
        """提取文件数据（使用增强的匹配功能）"""
        # 使用新的统一提取方法
        data = self.extractor.extract_from_file(file_path)

        if not data:
            return None

        # 使用模糊匹配增强参数匹配
        enhanced_params = {}

        # 分类处理参数
        ab_component_params = {}  # A/B组分参数
        regular_params = {}       # 普通参数

        for param_key, param_value in data['parameters'].items():
            # 检查是否为A/B组分参数
            if self._is_ab_component_param(param_key):
                ab_component_params[param_key] = param_value
            else:
                regular_params[param_key] = param_value

        # 处理A/B组分参数
        for param_key, param_value in ab_component_params.items():
            # 保持原始的A/B组分标识
            enhanced_params[param_key] = param_value
            logger.debug(f"A/B组分参数: '{param_key}' = '{param_value}'")

        # 处理普通参数（使用模糊匹配）
        for param_key, param_value in regular_params.items():
            matched_param = self.fuzzy_matcher.match_parameter(param_key, data['parameters'])
            if matched_param:
                enhanced_params[matched_param] = param_value
                logger.debug(f"参数匹配成功: '{param_key}' -> '{matched_param}'")
            else:
                enhanced_params[param_key] = param_value
                logger.debug(f"参数未匹配: '{param_key}' 保持原样")

        data['parameters'] = enhanced_params
        return data

    def _is_ab_component_param(self, param_key: str) -> bool:
        """判断是否为A/B组分参数

        A/B组分只存在于：
        1. 液体性能（新格式）
        2. 常规性能（老格式）

        工艺性能中的产品型号+A/B（如3801A/B）不是A/B组分，而是单一产品标识
        """
        param_lower = param_key.lower()

        # 只有这些基础参数才可能有A/B组分
        liquid_performance_params = ['外观', '粘度', '密度', '保存期限']

        # 检查是否为液体性能相关参数
        is_liquid_param = any(base_param in param_key for base_param in liquid_performance_params)

        if not is_liquid_param:
            return False

        # 检查A/B组分标识
        ab_indicators = ['(a)', '(b)', 'a)', 'b)']
        has_ab_indicator = any(indicator in param_lower for indicator in ab_indicators)

        # 额外检查：明确的产品型号+A/B格式，但只限于液体性能参数
        import re
        if is_liquid_param and re.search(r'(FM-[A-Z0-9-]+[AB]|\d+-\d+[AB])', param_key):
            return True

        return has_ab_indicator and is_liquid_param
    
    def _generate_validation_report(self, validation_results):
        """生成验证报告"""
        if not validation_results:
            return
        
        summary = self.validator.generate_validation_summary(validation_results)
        
        logger.info("=" * 60)
        logger.info("数据质量报告:")
        logger.info(f"  总文件数: {summary['total_files']}")
        logger.info(f"  验证通过: {summary['valid_files']}")
        logger.info(f"  验证失败: {summary['invalid_files']}")
        logger.info(f"  验证通过率: {summary['validation_rate']:.1f}%")
        logger.info(f"  平均质量评分: {summary['average_quality_score']:.1f}")
        logger.info(f"  平均完成率: {summary['average_completion_rate']:.1f}%")
        
        if summary['common_missing_params']:
            logger.info("  常见缺失参数:")
            for param, count in summary['common_missing_params']:
                logger.info(f"    {param}: {count}次")
        
        logger.info("=" * 60)

def main():
    """主函数"""
    try:
        print("=" * 60)
        print("🚀 智能汇总表生成器 v2.0")
        print("=" * 60)
        print("请选择运行模式:")
        print("1. 📊 数据提取模式 - 从PDF/Word提取数据生成Excel汇总表")
        print("2. 🌐 文档翻译模式 - 将PDF/Word文档翻译为英文版本")
        print("=" * 60)

        while True:
            try:
                choice = input("请输入选择 (1 或 2): ").strip()
                if choice in ['1', '2']:
                    break
                else:
                    print("❌ 无效选择，请输入 1 或 2")
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                return

        generator = SmartTableGeneratorV2()

        if choice == '1':
            print("\n📊 启动数据提取模式...")
            generator.process_folder()
        elif choice == '2':
            print("\n🌐 启动文档翻译模式...")
            generator.translate_documents()

    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
