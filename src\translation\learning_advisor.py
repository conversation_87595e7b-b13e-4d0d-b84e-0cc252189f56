#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学习建议系统
整合术语提取、翻译质量分析和学习建议生成功能
"""

import re
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from collections import Counter
from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class LearningItem:
    """学习项目"""
    item_type: str  # 'term', 'pattern', 'improvement'
    chinese: str
    english: str
    confidence: float
    context: str
    priority: str  # 'high', 'medium', 'low'
    source: str
    suggestions: List[str]


@dataclass
class LearningReport:
    """学习报告"""
    total_segments: int
    success_rate: float
    common_issues: List[str]
    learning_items: List[LearningItem]
    improvement_suggestions: List[str]
    quality_summary: Dict[str, Any]


class LearningAdvisor:
    """
    学习建议系统
    
    功能：
    1. 分析翻译结果，提取学习机会
    2. 识别常见翻译问题
    3. 生成术语学习建议
    4. 提供系统改进建议
    5. 生成学习报告
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # 术语提取器
        try:
            from ..learning.term_extractor import TermExtractor
            self.term_extractor = TermExtractor()
        except Exception as e:
            self.logger.warning(f"术语提取器初始化失败: {e}")
            self.term_extractor = None
        
        # 常见问题模式
        self.issue_patterns = {
            '中文残留': r'[\u4e00-\u9fff]+',
            '数字格式错误': r'\d+\s+\d+',
            '单位分离': r'\d+\s+[A-Za-z]+',
            '重复翻译': r'(\w+)\s+\1',
            '过长单词': r'[A-Za-z]{20,}',
        }
        
        # 优先级权重
        self.priority_weights = {
            'technical_term': 0.9,
            'high_frequency': 0.8,
            'quality_issue': 0.7,
            'format_error': 0.6,
            'general_term': 0.3
        }
    
    def analyze_translation_session(self, translation_context) -> LearningReport:
        """
        分析翻译会话，生成学习报告
        
        Args:
            translation_context: 翻译上下文对象
            
        Returns:
            LearningReport: 学习报告
        """
        segments = translation_context.segments
        quality_metrics = translation_context.quality_metrics
        
        # 1. 基础统计
        total_segments = len(segments)
        success_rate = quality_metrics.get('success_rate', 0.0)
        
        # 2. 分析常见问题
        common_issues = self._analyze_common_issues(segments)
        
        # 3. 提取学习项目
        learning_items = self._extract_learning_items(segments)
        
        # 4. 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(segments, quality_metrics)
        
        # 5. 创建质量摘要
        quality_summary = self._create_quality_summary(segments, quality_metrics)
        
        return LearningReport(
            total_segments=total_segments,
            success_rate=success_rate,
            common_issues=common_issues,
            learning_items=learning_items,
            improvement_suggestions=improvement_suggestions,
            quality_summary=quality_summary
        )
    
    def _analyze_common_issues(self, segments) -> List[str]:
        """分析常见问题"""
        issue_counts = Counter()
        
        for segment in segments:
            # 检查翻译失败的原因
            if hasattr(segment, 'error_message') and segment.error_message:
                issue_counts[segment.error_message] += 1
            
            # 检查质量问题
            if segment.translated_text:
                for issue_name, pattern in self.issue_patterns.items():
                    if re.search(pattern, segment.translated_text):
                        issue_counts[issue_name] += 1
        
        # 返回最常见的5个问题
        return [issue for issue, count in issue_counts.most_common(5)]
    
    def _extract_learning_items(self, segments) -> List[LearningItem]:
        """提取学习项目"""
        learning_items = []
        
        # 收集失败和部分成功的片段
        problematic_segments = [
            s for s in segments 
            if hasattr(s, 'result_status') and s.result_status.value in ['failed', 'partial', 'needs_manual']
        ]
        
        for segment in problematic_segments:
            # 1. 提取未翻译的术语
            untranslated_terms = self._extract_untranslated_terms(segment)
            learning_items.extend(untranslated_terms)
            
            # 2. 提取部分翻译的术语
            if segment.translated_text:
                partial_terms = self._extract_partial_translation_terms(segment)
                learning_items.extend(partial_terms)
        
        # 3. 从成功翻译中提取可能的术语对
        successful_segments = [
            s for s in segments 
            if hasattr(s, 'result_status') and s.result_status.value == 'success'
        ]
        
        for segment in successful_segments:
            if segment.translation_method == "腾讯翻译":  # AI翻译的结果可能包含新术语
                ai_terms = self._extract_ai_translation_terms(segment)
                learning_items.extend(ai_terms)
        
        # 去重和排序
        learning_items = self._deduplicate_and_prioritize(learning_items)
        
        return learning_items[:20]  # 限制数量
    
    def _extract_untranslated_terms(self, segment) -> List[LearningItem]:
        """提取未翻译的术语"""
        items = []
        
        # 查找中文术语
        chinese_terms = re.findall(r'[\u4e00-\u9fff]{2,}', segment.original_text)
        
        for term in chinese_terms:
            if self._is_technical_term(term):
                item = LearningItem(
                    item_type='term',
                    chinese=term,
                    english='[需要人工翻译]',
                    confidence=0.8,
                    context=segment.original_text[:50],
                    priority='high',
                    source='untranslated',
                    suggestions=[f'请提供 "{term}" 的英文翻译']
                )
                items.append(item)
        
        return items
    
    def _extract_partial_translation_terms(self, segment) -> List[LearningItem]:
        """提取部分翻译的术语"""
        items = []
        
        if not segment.translated_text:
            return items
        
        # 查找翻译中残留的中文
        remaining_chinese = re.findall(r'[\u4e00-\u9fff]{2,}', segment.translated_text)
        
        for chinese_term in remaining_chinese:
            if chinese_term in segment.original_text:
                item = LearningItem(
                    item_type='term',
                    chinese=chinese_term,
                    english='[部分翻译失败]',
                    confidence=0.6,
                    context=segment.original_text[:50],
                    priority='medium',
                    source='partial_translation',
                    suggestions=[f'"{chinese_term}" 需要更准确的翻译']
                )
                items.append(item)
        
        return items
    
    def _extract_ai_translation_terms(self, segment) -> List[LearningItem]:
        """从AI翻译结果中提取术语对"""
        items = []
        
        if not self.term_extractor:
            return items
        
        try:
            # 使用术语提取器分析AI翻译结果
            candidates = self.term_extractor.extract_from_ai_translation(
                segment.original_text, 
                segment.translated_text
            )
            
            for candidate in candidates[:3]:  # 限制数量
                item = LearningItem(
                    item_type='term',
                    chinese=candidate.chinese,
                    english=candidate.english,
                    confidence=candidate.confidence,
                    context=candidate.context,
                    priority='medium',
                    source='ai_extraction',
                    suggestions=[f'验证术语对: "{candidate.chinese}" -> "{candidate.english}"']
                )
                items.append(item)
                
        except Exception as e:
            self.logger.debug(f"AI术语提取失败: {e}")
        
        return items
    
    def _is_technical_term(self, term: str) -> bool:
        """判断是否为技术术语"""
        technical_keywords = {
            '性能', '测试', '标准', '条件', '温度', '湿度', '强度', '硬度',
            '粘度', '密度', '时间', '比例', '重量', '体积', '压力', '电阻',
            '绝缘', '阻燃', '防水', '防潮', '防震', '防霉', '固化', '混合',
            '搅拌', '浇注', '灌封', '密封', '储存', '运输', '包装', '规格'
        }
        
        return any(keyword in term for keyword in technical_keywords)
    
    def _deduplicate_and_prioritize(self, items: List[LearningItem]) -> List[LearningItem]:
        """去重和优先级排序"""
        # 按中文术语去重
        seen_terms = set()
        unique_items = []
        
        for item in items:
            if item.chinese not in seen_terms:
                seen_terms.add(item.chinese)
                unique_items.append(item)
        
        # 按优先级和置信度排序
        def priority_score(item):
            priority_scores = {'high': 3, 'medium': 2, 'low': 1}
            return priority_scores.get(item.priority, 0) + item.confidence
        
        unique_items.sort(key=priority_score, reverse=True)
        return unique_items
    
    def _generate_improvement_suggestions(self, segments, quality_metrics) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        success_rate = quality_metrics.get('success_rate', 0.0)
        avg_chinese_remaining = quality_metrics.get('avg_chinese_remaining', 0.0)
        
        # 基于成功率的建议
        if success_rate < 0.6:
            suggestions.append("翻译成功率较低，建议扩充技术词典或优化分段策略")
        elif success_rate < 0.8:
            suggestions.append("翻译成功率中等，建议针对性地添加常见术语到词典")
        
        # 基于中文残留的建议
        if avg_chinese_remaining > 0.3:
            suggestions.append("中文残留较多，建议使用更精细的分段策略")
        elif avg_chinese_remaining > 0.1:
            suggestions.append("存在少量中文残留，建议优化术语词典覆盖范围")
        
        # 基于翻译方法分布的建议
        method_counts = Counter()
        for segment in segments:
            if hasattr(segment, 'translation_method') and segment.translation_method:
                method_counts[segment.translation_method] += 1
        
        if method_counts.get('腾讯翻译', 0) > len(segments) * 0.7:
            suggestions.append("过度依赖云端翻译，建议扩充本地词典以降低成本")
        
        return suggestions
    
    def _create_quality_summary(self, segments, quality_metrics) -> Dict[str, Any]:
        """创建质量摘要"""
        # 统计翻译方法分布
        method_counts = Counter()
        for segment in segments:
            if hasattr(segment, 'translation_method') and segment.translation_method:
                method_counts[segment.translation_method] += 1
        
        # 统计问题类型
        issue_counts = Counter()
        for segment in segments:
            if hasattr(segment, 'error_message') and segment.error_message:
                issue_counts[segment.error_message] += 1
        
        return {
            'translation_methods': dict(method_counts),
            'common_errors': dict(issue_counts.most_common(5)),
            'quality_metrics': quality_metrics,
            'recommendations': {
                'dictionary_expansion': len([s for s in segments if hasattr(s, 'result_status') and s.result_status.value == 'failed']),
                'segmentation_optimization': len([s for s in segments if hasattr(s, 'chinese_remaining') and s.chinese_remaining > 0.2])
            }
        }
    
    def format_learning_report(self, report: LearningReport, format_type: str = 'text') -> str:
        """格式化学习报告"""
        if format_type == 'text':
            return self._format_text_report(report)
        elif format_type == 'markdown':
            return self._format_markdown_report(report)
        else:
            return self._format_text_report(report)
    
    def _format_text_report(self, report: LearningReport) -> str:
        """格式化为文本报告"""
        lines = []
        lines.append("=" * 60)
        lines.append("📚 翻译学习报告")
        lines.append("=" * 60)
        
        # 基础统计
        lines.append(f"\n📊 基础统计:")
        lines.append(f"  总片段数: {report.total_segments}")
        lines.append(f"  成功率: {report.success_rate:.1%}")
        
        # 常见问题
        if report.common_issues:
            lines.append(f"\n⚠️  常见问题:")
            for issue in report.common_issues:
                lines.append(f"  • {issue}")
        
        # 学习项目
        if report.learning_items:
            lines.append(f"\n📝 学习建议 ({len(report.learning_items)} 项):")
            for item in report.learning_items[:10]:  # 显示前10项
                lines.append(f"  • \"{item.chinese}\" -> \"{item.english}\" (置信度: {item.confidence:.2f})")
        
        # 改进建议
        if report.improvement_suggestions:
            lines.append(f"\n💡 改进建议:")
            for suggestion in report.improvement_suggestions:
                lines.append(f"  • {suggestion}")
        
        return '\n'.join(lines)
    
    def _format_markdown_report(self, report: LearningReport) -> str:
        """格式化为Markdown报告"""
        lines = []
        lines.append("# 📚 翻译学习报告\n")
        
        # 基础统计
        lines.append("## 📊 基础统计\n")
        lines.append(f"- **总片段数**: {report.total_segments}")
        lines.append(f"- **成功率**: {report.success_rate:.1%}\n")
        
        # 常见问题
        if report.common_issues:
            lines.append("## ⚠️ 常见问题\n")
            for issue in report.common_issues:
                lines.append(f"- {issue}")
            lines.append("")
        
        # 学习项目
        if report.learning_items:
            lines.append(f"## 📝 学习建议 ({len(report.learning_items)} 项)\n")
            lines.append("| 中文 | 英文 | 置信度 | 来源 |")
            lines.append("|------|------|--------|------|")
            for item in report.learning_items[:10]:
                lines.append(f"| {item.chinese} | {item.english} | {item.confidence:.2f} | {item.source} |")
            lines.append("")
        
        # 改进建议
        if report.improvement_suggestions:
            lines.append("## 💡 改进建议\n")
            for suggestion in report.improvement_suggestions:
                lines.append(f"- {suggestion}")
            lines.append("")
        
        return '\n'.join(lines)
