# 统一翻译系统依赖包
# 核心依赖
pandas>=1.5.0
openpyxl>=3.1.0
pdfplumber>=0.9.0
python-docx>=0.8.11

# 模糊匹配
rapidfuzz>=3.0.0

# 进度显示
tqdm>=4.64.0

# 日志增强
colorlog>=6.7.0

# 配置管理
pyyaml>=6.0

# 数据验证
jsonschema>=4.17.0

# 翻译功能
transformers>=4.21.0
torch>=1.12.0
reportlab>=3.6.0
tencentcloud-sdk-python>=3.0.0
sentencepiece>=0.1.97  # Helsinki-NLP 依赖

# 打包相关
pyinstaller>=5.10.0

# 开发工具（可选）
pytest>=7.2.0
black>=23.0.0
flake8>=6.0.0

# 注意：如果某些包安装失败，可以尝试：
# pip install --no-deps transformers torch
# 或者使用 conda 安装：
# conda install pytorch transformers -c pytorch
