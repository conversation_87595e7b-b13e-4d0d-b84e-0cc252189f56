#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反向翻译验证器
通过英译中再对比原文来验证翻译质量
"""

import re
import torch
from typing import Optional, Tuple
from ..utils import get_logger

logger = get_logger(__name__)

class ReverseTranslationValidator:
    """反向翻译验证器"""
    
    def __init__(self):
        self.en_to_zh_model = None
        self.en_to_zh_tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.en_to_zh_model_name = "Helsinki-NLP/opus-mt-en-zh"
        self.max_length = 512
        
        logger.info(f"初始化反向翻译验证器，设备: {self.device}")
    
    def _load_reverse_model(self):
        """加载英译中模型"""
        if self.en_to_zh_model is None:
            try:
                from transformers import MarianMTModel, MarianTokenizer
                
                logger.info("正在加载英译中模型...")
                self.en_to_zh_tokenizer = MarianTokenizer.from_pretrained(self.en_to_zh_model_name)
                self.en_to_zh_model = MarianMTModel.from_pretrained(self.en_to_zh_model_name)
                self.en_to_zh_model.to(self.device)
                self.en_to_zh_model.eval()
                
                logger.info("英译中模型加载完成")
                
            except Exception as e:
                logger.warning(f"加载英译中模型失败: {e}")
                logger.warning("将使用文本相似度作为替代验证方法")
                return False
        return True
    
    def translate_en_to_zh(self, english_text: str) -> Optional[str]:
        """英译中"""
        if not english_text or not english_text.strip():
            return None
        
        try:
            if not self._load_reverse_model():
                return None
            
            # 预处理
            processed_text = english_text.strip()
            
            # 编码
            inputs = self.en_to_zh_tokenizer(
                processed_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=self.max_length
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 翻译
            with torch.no_grad():
                translated = self.en_to_zh_model.generate(
                    **inputs,
                    max_length=self.max_length,
                    num_beams=4,
                    early_stopping=True
                )
            
            # 解码
            result = self.en_to_zh_tokenizer.decode(translated[0], skip_special_tokens=True)
            
            return result.strip()
            
        except Exception as e:
            logger.error(f"英译中失败: {e}")
            return None
    
    def validate_translation(self, original_chinese: str, english_translation: str) -> Tuple[float, str]:
        """
        验证翻译质量
        返回: (相似度分数, 验证说明)
        """
        if not original_chinese or not english_translation:
            return 0.0, "输入为空"
        
        # 方法1: 反向翻译验证
        reverse_chinese = self.translate_en_to_zh(english_translation)
        
        if reverse_chinese:
            similarity_score = self._calculate_similarity(original_chinese, reverse_chinese)
            
            if similarity_score > 0.8:
                return similarity_score, f"反向翻译验证通过 (相似度: {similarity_score:.2f})"
            elif similarity_score > 0.6:
                return similarity_score, f"反向翻译部分匹配 (相似度: {similarity_score:.2f})"
            else:
                return similarity_score, f"反向翻译差异较大 (相似度: {similarity_score:.2f})\n原文: {original_chinese}\n反译: {reverse_chinese}"
        
        # 方法2: 文本特征验证（回退方案）
        feature_score = self._validate_by_features(original_chinese, english_translation)
        return feature_score, f"基于特征验证 (分数: {feature_score:.2f})"
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个中文文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的字符级相似度
        char_similarity = self._char_level_similarity(text1, text2)
        
        # 词汇级相似度
        word_similarity = self._word_level_similarity(text1, text2)
        
        # 语义相似度（基于关键词）
        semantic_similarity = self._semantic_similarity(text1, text2)
        
        # 综合评分
        final_score = (char_similarity * 0.3 + word_similarity * 0.4 + semantic_similarity * 0.3)
        
        return min(1.0, final_score)
    
    def _char_level_similarity(self, text1: str, text2: str) -> float:
        """字符级相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 计算编辑距离
        def edit_distance(s1, s2):
            m, n = len(s1), len(s2)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(m + 1):
                dp[i][0] = i
            for j in range(n + 1):
                dp[0][j] = j
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if s1[i-1] == s2[j-1]:
                        dp[i][j] = dp[i-1][j-1]
                    else:
                        dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
            
            return dp[m][n]
        
        distance = edit_distance(text1, text2)
        max_len = max(len(text1), len(text2))
        
        if max_len == 0:
            return 1.0
        
        return 1.0 - (distance / max_len)
    
    def _word_level_similarity(self, text1: str, text2: str) -> float:
        """词汇级相似度"""
        # 提取关键词汇
        words1 = set(re.findall(r'[\u4e00-\u9fa5]{2,}', text1))
        words2 = set(re.findall(r'[\u4e00-\u9fa5]{2,}', text2))
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _semantic_similarity(self, text1: str, text2: str) -> float:
        """语义相似度（基于关键概念）"""
        # 定义关键概念映射
        concept_keywords = {
            '存储': ['储存', '贮存', '保存', '存放'],
            '温度': ['温度', '℃', '度'],
            '湿度': ['湿度', 'RH', '%'],
            '时间': ['时间', '期限', '小时', '天', '月'],
            '条件': ['条件', '环境', '要求'],
            '材料': ['材料', '组份', '成分'],
            '性能': ['性能', '特性', '效果'],
            '工艺': ['工艺', '过程', '流程', '步骤'],
        }
        
        def extract_concepts(text):
            concepts = set()
            for concept, keywords in concept_keywords.items():
                if any(keyword in text for keyword in keywords):
                    concepts.add(concept)
            return concepts
        
        concepts1 = extract_concepts(text1)
        concepts2 = extract_concepts(text2)
        
        if not concepts1 and not concepts2:
            return 0.5  # 中性分数
        if not concepts1 or not concepts2:
            return 0.2
        
        intersection = concepts1.intersection(concepts2)
        union = concepts1.union(concepts2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _validate_by_features(self, chinese: str, english: str) -> float:
        """基于特征的验证（回退方案）"""
        score = 0.5  # 基础分数
        
        # 检查长度合理性
        if 0.3 <= len(english) / len(chinese) <= 2.0:
            score += 0.2
        
        # 检查是否包含中文（应该没有）
        if not re.search(r'[\u4e00-\u9fa5]', english):
            score += 0.2
        
        # 检查关键词对应
        key_mappings = {
            '储存': ['storage', 'store'],
            '温度': ['temperature'],
            '湿度': ['humidity'],
            '密封': ['seal', 'sealed'],
            '避光': ['light', 'dark'],
            '阴暗': ['dark', 'shade'],
        }
        
        matched_concepts = 0
        total_concepts = 0
        
        for chinese_key, english_keys in key_mappings.items():
            if chinese_key in chinese:
                total_concepts += 1
                if any(eng_key in english.lower() for eng_key in english_keys):
                    matched_concepts += 1
        
        if total_concepts > 0:
            concept_score = matched_concepts / total_concepts
            score += concept_score * 0.3
        
        return min(1.0, score)
