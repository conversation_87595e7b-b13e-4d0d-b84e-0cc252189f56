#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
处理配置文件的加载、验证、更新和学习机制
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import jsonschema
from .logger import get_logger

logger = get_logger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json", learning_file: str = "learned_mappings.json"):
        self.config_file = Path(config_file)
        self.learning_file = Path(learning_file)
        self.config = {}
        self.learned_mappings = {}
        
        # 配置文件模式
        self.config_schema = {
            "type": "object",
            "properties": {
                "column_mapping": {"type": "object"},
                "param_mapping": {"type": "object"},
                "settings": {"type": "object"}
            },
            "required": ["column_mapping", "param_mapping", "settings"]
        }
        
        self.load_config()
        self.load_learned_mappings()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                
                # 验证配置文件格式
                jsonschema.validate(self.config, self.config_schema)
                logger.info(f"成功加载配置文件: {self.config_file}")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}，使用默认配置")
                self.config = self.get_default_config()
                self.save_config()
                
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            self.config = self.get_default_config()
        except jsonschema.ValidationError as e:
            logger.error(f"配置文件验证失败: {e}")
            self.config = self.get_default_config()
        except Exception as e:
            logger.error(f"加载配置文件时出错: {e}")
            self.config = self.get_default_config()
    
    def load_learned_mappings(self):
        """加载学习的映射关系"""
        try:
            if self.learning_file.exists():
                with open(self.learning_file, 'r', encoding='utf-8') as f:
                    self.learned_mappings = json.load(f)
                logger.info(f"加载学习映射: {len(self.learned_mappings.get('learned_mappings', {}))} 个映射")
            else:
                self.learned_mappings = self.get_default_learning_data()
                self.save_learned_mappings()
        except Exception as e:
            logger.error(f"加载学习映射时出错: {e}")
            self.learned_mappings = self.get_default_learning_data()
    
    def get_default_config(self):
        """获取默认配置"""
        return {
            "column_mapping": {
                'A': '序号', 'B': '产品型号', 'C': '外观(A)', 'D': '外观(B)', 
                'E': '粘度(A)', 'F': '粘度(B)', 'G': '密度(A)', 'H': '密度(B)',
                'I': '混合比例', 'J': '混合粘度', 'K': '固化后密度', 'L': '开放时间',
                'M': '不流动时间', 'N': '固化时间', 'O': '流淌性', 'P': '硬度',
                'Q': '拉伸强度', 'R': '断裂伸长率', 'S': '剪切强度', 'T': '绝缘强度',
                'U': '体积电阻率', 'V': '介电常数', 'W': '介质损耗因数', 'X': '导热系数',
                'Y': '线型膨胀系数', 'Z': '耐老化', 'AA': '阻燃', 'AB': '吸水率',
                'AC': '应用温度', 'AD': '耐候性', 'AE': '性能特点'
            },
            "param_mapping": {
                '外观(A)': ['外观'], '外观(B)': ['外观'], '粘度(A)': ['粘度'], '粘度(B)': ['粘度'],
                '密度(A)': ['密度'], '密度(B)': ['密度'], '混合比例': ['混合比例', '比例'],
                '混合粘度': ['混合粘度'], '固化后密度': ['混合密度', '固化后密度'],
                '开放时间': ['开放时间', '可操作时间', '可使用时长'],
                '不流动时间': ['不流动时间', '凝胶时间'], '固化时间': ['固化时间', '固化条件'],
                '流淌性': ['流淌性'], '硬度': ['硬度'], '拉伸强度': ['拉伸强度'],
                '断裂伸长率': ['断裂伸长率', '伸长率'], '剪切强度': ['剪切强度'],
                '绝缘强度': ['绝缘强度', '介电强度', '耐电压'], '体积电阻率': ['体积电阻率', '电阻率'],
                '介电常数': ['介电常数'], '介质损耗因数': ['介质损耗因数', '损耗因数'],
                '导热系数': ['导热系数'], '线型膨胀系数': ['线性膨胀系数', '膨胀系数', '线型膨胀系数'],
                '耐老化': ['耐老化'], '阻燃': ['阻燃'], '吸水率': ['吸水率', '吸湿性'],
                '应用温度': ['应用温度范围', '应用温度', '使用温度'], '耐候性': ['耐候性'],
                '性能特点': ['产品陈述', '性能特点', '产品描述', '产品概述']
            },
            "settings": {
                "input_folder": "PDF",
                "output_file": "智能汇总表.xlsx",
                "data_start_row": 8,
                "header_row": 4,
                "ab_indicator_row": 5,
                "fuzzy_match_threshold": 85,
                "enable_learning": True,
                "auto_save_config": True
            }
        }
    
    def get_default_learning_data(self):
        """获取默认学习数据"""
        return {
            "learned_mappings": {},
            "confidence_scores": {},
            "usage_statistics": {
                "total_files": 0,
                "successful_matches": 0,
                "failed_matches": 0,
                "learning_sessions": 0
            },
            "version": "1.0"
        }
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            logger.debug(f"配置文件已保存: {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件时出错: {e}")
    
    def save_learned_mappings(self):
        """保存学习的映射关系"""
        try:
            with open(self.learning_file, 'w', encoding='utf-8') as f:
                json.dump(self.learned_mappings, f, ensure_ascii=False, indent=2)
            logger.debug(f"学习映射已保存: {self.learning_file}")
        except Exception as e:
            logger.error(f"保存学习映射时出错: {e}")
    
    def get_param_mapping(self) -> Dict[str, List[str]]:
        """获取参数映射（包含学习的映射）"""
        base_mapping = self.config.get("param_mapping", {})
        learned_mapping = self.learned_mappings.get("learned_mappings", {})
        
        # 合并基础映射和学习映射
        combined_mapping = base_mapping.copy()
        for param, keywords in learned_mapping.items():
            if param in combined_mapping:
                # 合并关键词，去重
                combined_keywords = list(set(combined_mapping[param] + keywords))
                combined_mapping[param] = combined_keywords
            else:
                combined_mapping[param] = keywords
        
        return combined_mapping
    
    def learn_mapping(self, param_name: str, matched_keyword: str, confidence: float = 1.0):
        """学习新的映射关系"""
        if not self.config.get("settings", {}).get("enable_learning", True):
            return
        
        learned_mappings = self.learned_mappings.get("learned_mappings", {})
        confidence_scores = self.learned_mappings.get("confidence_scores", {})
        
        if param_name not in learned_mappings:
            learned_mappings[param_name] = []
        
        if matched_keyword not in learned_mappings[param_name]:
            learned_mappings[param_name].append(matched_keyword)
            confidence_scores[param_name] = confidence
            
            self.learned_mappings["learned_mappings"] = learned_mappings
            self.learned_mappings["confidence_scores"] = confidence_scores
            
            # 更新统计信息
            stats = self.learned_mappings.get("usage_statistics", {})
            stats["learning_sessions"] = stats.get("learning_sessions", 0) + 1
            self.learned_mappings["usage_statistics"] = stats
            
            logger.info(f"学习新映射: {param_name} -> {matched_keyword} (置信度: {confidence:.2f})")
            
            if self.config.get("settings", {}).get("auto_save_config", True):
                self.save_learned_mappings()
    
    def get_setting(self, key: str, default=None):
        """获取设置值"""
        return self.config.get("settings", {}).get(key, default)
    
    def update_setting(self, key: str, value: Any):
        """更新设置值"""
        if "settings" not in self.config:
            self.config["settings"] = {}
        
        self.config["settings"][key] = value
        
        if self.config.get("settings", {}).get("auto_save_config", True):
            self.save_config()
    
    def get_column_mapping(self) -> Dict[str, str]:
        """获取列映射"""
        return self.config.get("column_mapping", {})
    
    def backup_config(self, backup_suffix: str = None):
        """备份配置文件"""
        if backup_suffix is None:
            from datetime import datetime
            backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        backup_file = self.config_file.with_suffix(f".backup_{backup_suffix}.json")
        
        try:
            import shutil
            shutil.copy2(self.config_file, backup_file)
            logger.info(f"配置文件已备份: {backup_file}")
        except Exception as e:
            logger.error(f"备份配置文件时出错: {e}")

# 全局配置管理器实例
config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    return config_manager
