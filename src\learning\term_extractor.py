#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
术语提取器
从翻译结果中提取可学习的术语
"""

import re
import jieba
from typing import List, Tuple, Dict, Set
from dataclasses import dataclass

@dataclass
class TermCandidate:
    """术语候选"""
    chinese: str
    english: str
    confidence: float
    context: str
    term_type: str
    source_method: str  # ai/partial/compound

class TermExtractor:
    """术语提取器"""
    
    def __init__(self):
        # 初始化数据库连接以检查已有翻译
        try:
            from ..database import TranslationDatabase
            self.db = TranslationDatabase()
            self.existing_terms = self.db.get_all_terms()
        except Exception as e:
            print(f"⚠️  术语提取器数据库连接失败: {e}")
            self.db = None
            self.existing_terms = {}

        # 技术术语关键词
        self.technical_keywords = {
            '性能', '测试', '标准', '条件', '温度', '湿度', '强度', '硬度',
            '粘度', '密度', '时间', '比例', '重量', '体积', '压力', '电阻',
            '绝缘', '阻燃', '防水', '防潮', '防震', '防霉', '固化', '混合',
            '搅拌', '浇注', '灌封', '密封', '储存', '运输', '包装', '规格'
        }
        
        # 常见的技术术语模式
        self.term_patterns = [
            r'[\u4e00-\u9fa5]+性能',  # XX性能
            r'[\u4e00-\u9fa5]+条件',  # XX条件
            r'[\u4e00-\u9fa5]+标准',  # XX标准
            r'[\u4e00-\u9fa5]+工艺',  # XX工艺
            r'[\u4e00-\u9fa5]+过程',  # XX过程
            r'[\u4e00-\u9fa5]+方法',  # XX方法
            r'[\u4e00-\u9fa5]+材料',  # XX材料
            r'[\u4e00-\u9fa5]+设备',  # XX设备
            r'[\u4e00-\u9fa5]+产品',  # XX产品
        ]
    
    def extract_from_ai_translation(self, original: str, translated: str) -> List[TermCandidate]:
        """从AI翻译结果中提取术语候选"""
        candidates = []
        
        # 分词处理
        chinese_words = self._segment_chinese(original)
        english_words = self._segment_english(translated)
        
        # 提取技术术语
        tech_terms = self._extract_technical_terms(chinese_words)

        # 去重并按长度排序（优先处理长术语）
        tech_terms = sorted(set(tech_terms), key=len, reverse=True)

        # 避免重复：如果一个术语是另一个术语的子集，只保留长的
        filtered_terms = []
        for term in tech_terms:
            is_subset = False
            for existing_term in filtered_terms:
                if term in existing_term and term != existing_term:
                    is_subset = True
                    break
            if not is_subset:
                filtered_terms.append(term)

        for term in filtered_terms[:5]:  # 限制数量，避免过多候选
            # 检查是否已在数据库中存在高质量翻译
            if self.existing_terms and term in self.existing_terms:
                existing_translation = self.existing_terms[term]
                # 如果已有较长的翻译（通常质量更好），跳过
                if len(existing_translation) > 10:
                    continue

            # 尝试在英文中找到对应翻译
            possible_translations = self._find_possible_translations(term, english_words, translated)

            for eng_term, confidence in possible_translations[:2]:  # 每个术语最多2个候选
                candidate = TermCandidate(
                    chinese=term,
                    english=eng_term,
                    confidence=confidence,
                    context=original[:100],
                    term_type=self._classify_term_type(term),
                    source_method='ai'
                )
                candidates.append(candidate)
        
        return candidates
    
    def extract_from_partial_translation(self, original: str, translated: str) -> List[TermCandidate]:
        """从部分翻译结果中提取术语候选"""
        candidates = []
        
        # 找到未翻译的中文部分
        untranslated_chinese = re.findall(r'[\u4e00-\u9fa5]+', translated)
        
        for chinese_part in untranslated_chinese:
            if len(chinese_part) >= 2 and chinese_part in original:
                # 这是一个未翻译的术语，需要学习
                candidate = TermCandidate(
                    chinese=chinese_part,
                    english="",  # 待用户提供
                    confidence=0.8,
                    context=original[:100],
                    term_type=self._classify_term_type(chinese_part),
                    source_method='partial'
                )
                candidates.append(candidate)
        
        return candidates
    
    def _segment_chinese(self, text: str) -> List[str]:
        """中文分词"""
        # 使用jieba分词，但优先保留技术术语
        words = []
        
        # 先提取技术术语模式
        for pattern in self.term_patterns:
            matches = re.findall(pattern, text)
            words.extend(matches)
        
        # 再进行常规分词
        jieba_words = jieba.lcut(text)
        words.extend([w for w in jieba_words if len(w) >= 2])
        
        return list(set(words))  # 去重
    
    def _segment_english(self, text: str) -> List[str]:
        """英文分词"""
        if not text:
            return []

        # 简单的英文分词
        words = re.findall(r'\b[A-Za-z]+\b', text)
        return [w for w in words if len(w) >= 2]
    
    def _extract_technical_terms(self, words: List[str]) -> List[str]:
        """提取技术术语"""
        tech_terms = []

        for word in words:
            # 过滤过长的文本（可能是句子）
            if len(word) > 20:
                # 检查是否是句子（包含多个概念）
                if self._is_sentence_like(word):
                    # 如果是句子，尝试提取其中的关键短语
                    phrases = self._extract_key_phrases(word)
                    tech_terms.extend(phrases)
                    continue

            # 检查是否包含技术关键词
            if any(keyword in word for keyword in self.technical_keywords):
                tech_terms.append(word)
            # 检查是否匹配技术术语模式
            elif any(re.match(pattern, word) for pattern in self.term_patterns):
                tech_terms.append(word)
            # 检查长度和复杂度
            elif 3 <= len(word) <= 15 and self._is_likely_technical_term(word):
                tech_terms.append(word)

        return tech_terms

    def _is_sentence_like(self, text: str) -> bool:
        """判断是否像句子"""
        # 包含标点符号
        if any(punct in text for punct in '，。；：、'):
            return True
        # 包含多个技术关键词
        keyword_count = sum(1 for keyword in self.technical_keywords if keyword in text)
        if keyword_count >= 2:
            return True
        # 长度超过阈值
        if len(text) > 15:
            return True
        return False

    def _extract_key_phrases(self, sentence: str) -> List[str]:
        """从句子中提取关键短语"""
        phrases = []

        # 基于技术关键词分割
        for keyword in self.technical_keywords:
            if keyword in sentence:
                # 提取包含关键词的短语
                pattern = f'[^，。；：、]*{keyword}[^，。；：、]*'
                matches = re.findall(pattern, sentence)
                for match in matches:
                    if 3 <= len(match) <= 12:  # 合理的短语长度
                        phrases.append(match.strip())

        # 如果没有找到短语，尝试按标点分割
        if not phrases:
            parts = re.split(r'[，。；：、]', sentence)
            for part in parts:
                part = part.strip()
                if 3 <= len(part) <= 12:
                    phrases.append(part)

        return phrases[:3]  # 最多返回3个短语
    
    def _is_likely_technical_term(self, word: str) -> bool:
        """判断是否可能是技术术语"""
        # 简单的启发式规则
        if len(word) >= 4:  # 长词汇更可能是技术术语
            return True
        if any(char in word for char in '型胶剂料品件器具机设备'):
            return True
        return False
    
    def _find_possible_translations(self, chinese_term: str, english_words: List[str], full_translation: str) -> List[Tuple[str, float]]:
        """在英文翻译中找到可能的对应词汇"""
        candidates = []
        
        # 方法1：基于位置的匹配
        chinese_pos = full_translation.find(chinese_term)
        if chinese_pos == -1:
            # 中文术语在翻译中被替换了，尝试找到对应的英文
            # 这里可以使用更复杂的对齐算法
            pass
        
        # 方法2：基于语义的匹配（简化版）
        for word in english_words:
            if len(word) >= 3:  # 只考虑较长的英文词汇
                confidence = self._calculate_semantic_similarity(chinese_term, word)
                if confidence > 0.3:
                    candidates.append((word, confidence))
        
        # 方法3：基于常见翻译模式
        common_patterns = {
            '性能': ['Performance', 'Properties'],
            '条件': ['Conditions', 'Requirements'],
            '工艺': ['Process', 'Technology'],
            '材料': ['Material', 'Materials'],
            '设备': ['Equipment', 'Device'],
        }
        
        for chinese_key, english_options in common_patterns.items():
            if chinese_key in chinese_term:
                for eng_option in english_options:
                    if eng_option.lower() in full_translation.lower():
                        candidates.append((eng_option, 0.9))
        
        # 按置信度排序并去重
        candidates = sorted(set(candidates), key=lambda x: x[1], reverse=True)
        return candidates[:3]  # 返回前3个候选
    
    def _calculate_semantic_similarity(self, chinese: str, english: str) -> float:
        """计算语义相似度（简化版）"""
        # 这里可以使用更复杂的语义相似度算法
        # 目前使用简单的启发式规则
        
        # 基于长度的相似度
        length_similarity = min(len(chinese), len(english)) / max(len(chinese), len(english))
        
        # 基于常见对应关系
        common_mappings = {
            '性能': 'performance',
            '条件': 'condition',
            '工艺': 'process',
            '材料': 'material',
            '设备': 'equipment',
            '产品': 'product',
            '测试': 'test',
            '标准': 'standard',
        }
        
        semantic_score = 0.0
        for ch_part, en_part in common_mappings.items():
            if ch_part in chinese and en_part.lower() in english.lower():
                semantic_score = 0.8
                break
        
        return max(length_similarity * 0.3, semantic_score)
    
    def _classify_term_type(self, term: str) -> str:
        """分类术语类型"""
        if any(keyword in term for keyword in ['性能', '测试', '标准']):
            return 'technical'
        elif any(keyword in term for keyword in ['工艺', '过程', '方法']):
            return 'process_flow'
        elif any(keyword in term for keyword in ['材料', '产品', '设备']):
            return 'compound'
        else:
            return 'basic'
