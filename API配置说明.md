# 腾讯翻译API配置说明

## 🔑 获取API密钥

### 1. 注册腾讯云账号
- 访问：https://cloud.tencent.com/
- 注册并完成实名认证

### 2. 开通机器翻译服务
- 进入控制台：https://console.cloud.tencent.com/tmt
- 开通机器翻译服务
- 查看免费额度：每月500万字符

### 3. 创建API密钥
- 访问：https://console.cloud.tencent.com/cam/capi
- 点击"新建密钥"
- 记录 `SecretId` 和 `SecretKey`

## ⚙️ 配置方法

### 在项目根目录创建 `.env` 文件：
```
TENCENT_SECRET_ID=你的SecretId
TENCENT_SECRET_KEY=你的SecretKey
```

**注意**：
- 不要将.env文件提交到代码仓库
- 确保密钥信息安全

## 🧪 验证配置

运行测试脚本：
```bash
python test_translation.py
```

如果配置正确，会显示：
```
✅ 成功加载.env配置文件
✅ 翻译器测试完成
```

## 📊 使用说明

### 启动程序
```bash
python quick_start.py
```

### 翻译模式
- **混合模式**：本地翻译 + 云端备份（推荐）
- **仅云端模式**：仅使用腾讯API（需配置密钥）

## 💰 费用说明

### 免费额度
- **每月免费**：500万字符
- **超出收费**：0.058元/万字符

### 预估使用量
- 22个技术文档约4.4万字符
- 完全在免费范围内

## 🔧 故障排除

### 常见错误

1. **认证失败**
   ```
   腾讯翻译API凭证未配置
   ```
   - 检查.env文件是否存在
   - 检查密钥是否正确

2. **频率限制**
   ```
   RequestLimitExceeded
   ```
   - 程序会自动处理，无需干预

3. **额度不足**
   ```
   NoFreeAmount
   ```
   - 检查月使用量是否超限

## 📁 文件结构
```
项目根目录/
├── .env                    # API配置文件（需要创建）
├── PDF/                    # 输入文件夹
├── PDF_EN/                 # 输出文件夹
├── quick_start.py          # 启动脚本
└── test_translation.py     # 测试脚本
```
