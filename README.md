# 智能汇总表生成器 v2.0

## 项目简介

智能汇总表生成器是一个自动化工具，能够从PDF和Word技术参数文档中提取数据，并生成标准化的Excel汇总表格。

## 🚀 v2.0 新功能

- **智能模糊匹配**：使用先进的模糊匹配算法，提高参数识别准确率
- **学习机制**：程序会自动学习用户的修正，越用越智能
- **数据验证**：自动检查数据完整性和质量，生成详细报告
- **详细日志**：分级日志记录，便于问题排查和性能分析
- **进度显示**：实时显示处理进度和统计信息
- **模块化架构**：代码结构清晰，易于维护和扩展

## 功能特性

- **多格式支持**：支持PDF (.pdf) 和Word (.docx) 文档
- **智能识别**：自动识别产品名称、技术参数和A/B组分数据
- **自动汇总**：生成标准化的Excel汇总表格
- **批量处理**：一次性处理整个文件夹中的所有文档
- **质量控制**：数据验证和质量评分
- **智能学习**：自动学习和优化参数匹配规则

## 系统要求

### Python版本
- Python 3.7 或更高版本

### 依赖包安装
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install pandas openpyxl pdfplumber python-docx rapidfuzz tqdm colorlog pyyaml jsonschema
```

## 使用方法

### 开发环境运行
1. **准备文件**：将PDF和Word技术参数文档放入 `PDF` 文件夹
2. **运行程序**：`python main.py`
3. **查看结果**：检查生成的 `智能汇总表.xlsx` 文件和日志

### 打包为exe文件
```bash
python build.py
```
打包后的文件在 `dist` 目录中。

### 生产环境部署
1. 将打包好的exe文件复制到目标机器
2. 确保 `config.json` 配置文件在同一目录
3. 创建 `PDF` 文件夹并放入待处理文档
4. 双击运行exe文件

## 项目结构

```
智能汇总表生成器/
├── src/                          # 源代码目录
│   ├── utils/                    # 工具模块
│   │   ├── logger.py            # 日志系统
│   │   ├── progress.py          # 进度显示
│   │   └── config_manager.py    # 配置管理
│   ├── validation/              # 数据验证模块
│   │   └── data_validator.py    # 数据验证器
│   ├── matching/                # 匹配模块
│   │   └── fuzzy_matcher.py     # 模糊匹配器
│   └── __init__.py
├── PDF/                         # 存放待处理的文档
├── logs/                        # 日志文件目录
├── main.py                      # 主程序入口
├── config.json                  # 配置文件
├── learned_mappings.json        # 学习的映射关系
├── requirements.txt             # 依赖包列表
├── build.py                     # 打包脚本
├── setup.py                     # 安装脚本
└── README.md                    # 项目说明
```

## 支持的数据类型

### 基本信息
- 产品型号
- 产品陈述/概述

### 液体性能（A/B组分）
- 外观
- 粘度
- 密度

### 工艺性能
- 混合比例
- 混合粘度
- 固化后密度
- 开放时间
- 不流动时间
- 固化时间
- 流淌性

### 固体性能
- 硬度
- 拉伸强度
- 断裂伸长率
- 剪切强度
- 绝缘强度
- 体积电阻率
- 介电常数
- 介质损耗因数
- 导热系数
- 线型膨胀系数
- 耐老化
- 阻燃
- 吸水率
- 应用温度
- 耐候性

## 配置说明

### 基本配置

程序的所有配置都保存在 `config.json` 文件中，用户可以直接编辑此文件来自定义：

- **输入输出设置**：文件夹路径、输出文件名
- **字段映射**：Excel列与参数名称的对应关系
- **参数匹配**：如何从原始文档中识别参数

### 快速配置示例

修改输入文件夹和输出文件名：
```json
"settings": {
    "input_folder": "技术文档",
    "output_file": "产品参数汇总.xlsx"
}
```

添加新的参数匹配：
```json
"硬度": ["硬度", "Shore硬度", "邵氏硬度", "硬度值"]
```

详细配置说明请参考 `配置说明.md` 文件。

## 注意事项

1. **文档格式**：确保PDF和Word文档包含标准的技术参数表格结构
2. **文件命名**：建议文件名包含产品型号，便于自动识别
3. **数据质量**：脚本会自动跳过无法解析的文档，并在控制台显示处理状态
4. **备份数据**：建议在处理重要文档前先备份原始文件

## 故障排除

### 常见问题

**Q: 脚本运行时提示缺少依赖包**
A: 请确保已安装所有必需的Python包：
```bash
pip install pandas openpyxl pdfplumber python-docx
```

**Q: 某些文档没有被处理**
A: 检查文档格式是否为PDF或DOCX，以及文档是否包含标准的技术参数表格

**Q: 生成的汇总表中某些数据为空**
A: 这可能是因为原始文档中的参数名称与脚本预期的格式不完全匹配，可以手动调整参数映射

## 技术支持

如有问题或建议，请检查控制台输出的详细日志信息，其中包含了处理过程的详细状态。
