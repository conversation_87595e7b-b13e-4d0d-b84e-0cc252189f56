#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合翻译器
Helsinki-NLP + 腾讯API 混合方案
"""

import re
import time
import torch
from typing import List, Optional, Dict
from ..utils import get_logger

logger = get_logger(__name__)

class HelsinkiTranslator:
    """Helsinki-NLP本地翻译器 - 优化版本"""

    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model_name = "Helsinki-NLP/opus-mt-zh-en"
        self.max_length = 512

        logger.info(f"初始化Helsinki翻译器，设备: {self.device}")

    def _load_model(self):
        """延迟加载模型"""
        if self.model is None:
            try:
                from transformers import MarianMTModel, MarianTokenizer

                logger.info("正在加载Helsinki-NLP模型...")
                self.tokenizer = MarianTokenizer.from_pretrained(self.model_name)
                self.model = MarianMTModel.from_pretrained(self.model_name)
                self.model.to(self.device)
                self.model.eval()  # 设置为评估模式

                logger.info("Helsinki-NLP模型加载完成")

            except Exception as e:
                logger.error(f"加载Helsinki-NLP模型失败: {e}")
                logger.error("请确保已安装sentencepiece: pip install sentencepiece")
                raise

    def translate(self, text: str) -> str:
        """翻译单个文本"""
        if not text or not text.strip():
            return text

        try:
            self._load_model()

            # 预处理文本
            processed_text = self._preprocess_text(text)

            # 编码
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=self.max_length
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 翻译
            with torch.no_grad():
                translated = self.model.generate(
                    **inputs,
                    max_length=self.max_length,
                    num_beams=4,
                    early_stopping=True
                )

            # 解码
            result = self.tokenizer.decode(translated[0], skip_special_tokens=True)

            # 后处理
            result = self._postprocess_text(result, text)

            logger.debug(f"翻译成功: '{text[:50]}...' -> '{result[:50]}...'")
            return result

        except Exception as e:
            logger.error(f"Helsinki翻译失败: {e}")
            raise

    def translate_batch(self, texts: List[str]) -> List[str]:
        """批量翻译"""
        if not texts:
            return []

        try:
            self._load_model()

            # 预处理
            processed_texts = [self._preprocess_text(text) for text in texts]

            # 编码
            inputs = self.tokenizer(
                processed_texts,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=self.max_length
            )
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # 翻译
            with torch.no_grad():
                translated = self.model.generate(
                    **inputs,
                    max_length=self.max_length,
                    num_beams=4,
                    early_stopping=True
                )

            # 解码
            results = []
            for i, translation in enumerate(translated):
                result = self.tokenizer.decode(translation, skip_special_tokens=True)
                result = self._postprocess_text(result, texts[i])
                results.append(result)

            logger.debug(f"批量翻译完成: {len(texts)} 个文本")
            return results

        except Exception as e:
            logger.error(f"批量翻译失败: {e}")
            raise

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 保护数值和单位
        text = re.sub(r'(\d+(?:\.\d+)?)\s*([A-Za-z·℃%]+)', r'\1 \2', text)

        # 保护产品型号
        text = re.sub(r'(FM-[A-Z0-9-]+)', r' \1 ', text)

        # 清理多余空格
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _postprocess_text(self, translated: str, original: str) -> str:
        """后处理翻译结果"""
        # 恢复数值格式
        translated = re.sub(r'(\d+(?:\.\d+)?)\s+([A-Za-z·℃%]+)', r'\1\2', translated)

        # 保持原始的标点符号风格
        if original.endswith('：'):
            if not translated.endswith(':'):
                translated = translated.rstrip('.,;') + ':'

        return translated.strip()



class TencentTranslator:
    """腾讯翻译API - 优化批量处理和频率控制"""

    def __init__(self, secret_id: str = None, secret_key: str = None):
        self.secret_id = secret_id
        self.secret_key = secret_key
        self.client = None

        # API限制配置
        self.max_chars_per_request = 5000  # 单次请求最大字符数（留1000字符缓冲）
        self.requests_per_second = 5  # 每秒最大请求数
        self.request_interval = 1.2  # 请求间隔（秒），略大于1/5秒
        self.last_request_time = 0

        # 批量处理配置
        self.batch_separator = "\n###BATCH_SEPARATOR###\n"  # 批量分隔符

        # 如果没有提供密钥，从环境变量或配置文件读取
        if not self.secret_id or not self.secret_key:
            self._load_credentials()
    
    def _load_credentials(self):
        """加载API凭证"""
        import os
        from pathlib import Path

        # 尝试加载.env文件
        self._load_env_file()

        self.secret_id = os.getenv('TENCENT_SECRET_ID')
        self.secret_key = os.getenv('TENCENT_SECRET_KEY')

        if not self.secret_id or not self.secret_key:
            logger.warning("腾讯翻译API凭证未配置，将跳过云端翻译")
            logger.info("请在项目根目录创建.env文件并设置:")
            logger.info("TENCENT_SECRET_ID=你的SecretId")
            logger.info("TENCENT_SECRET_KEY=你的SecretKey")

    def _load_env_file(self):
        """加载.env文件"""
        import os
        from pathlib import Path

        # 查找.env文件的可能位置
        possible_paths = [
            Path.cwd() / '.env',  # 当前工作目录
            Path(__file__).parent.parent.parent / '.env',  # 项目根目录
            Path.cwd().parent / '.env',  # 上级目录
        ]

        for env_path in possible_paths:
            if env_path.exists():
                logger.debug(f"找到.env文件: {env_path}")
                try:
                    with open(env_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#') and '=' in line:
                                key, value = line.split('=', 1)
                                os.environ[key.strip()] = value.strip()
                    logger.info("成功加载.env配置文件")
                    return
                except Exception as e:
                    logger.warning(f"加载.env文件失败: {e}")

        logger.debug("未找到.env文件")
    
    def _get_client(self):
        """获取腾讯云客户端"""
        if self.client is None and self.secret_id and self.secret_key:
            try:
                from tencentcloud.common import credential
                from tencentcloud.common.profile.client_profile import ClientProfile
                from tencentcloud.common.profile.http_profile import HttpProfile
                from tencentcloud.tmt.v20180321 import tmt_client
                
                cred = credential.Credential(self.secret_id, self.secret_key)
                httpProfile = HttpProfile()
                httpProfile.endpoint = "tmt.tencentcloudapi.com"
                
                clientProfile = ClientProfile()
                clientProfile.httpProfile = httpProfile
                
                self.client = tmt_client.TmtClient(cred, "ap-beijing", clientProfile)
                
            except Exception as e:
                logger.error(f"初始化腾讯翻译客户端失败: {e}")
        
        return self.client
    
    def translate(self, text: str) -> str:
        """翻译单个文本"""
        if not text or not text.strip():
            return text

        # 对于单个文本，直接调用批量翻译
        results = self.translate_batch([text])
        return results[0] if results else text

    def translate_batch(self, texts: List[str]) -> List[str]:
        """批量翻译文本 - 智能合并请求"""
        if not texts:
            return []

        client = self._get_client()
        if not client:
            raise Exception("腾讯翻译客户端未初始化")

        # 过滤空文本
        valid_texts = [(i, text.strip()) for i, text in enumerate(texts) if text and text.strip()]
        if not valid_texts:
            return texts

        try:
            # 智能分批处理
            batches = self._create_smart_batches([text for _, text in valid_texts])
            all_results = []

            for batch_texts in batches:
                batch_result = self._translate_single_batch(batch_texts)
                all_results.extend(batch_result)

            # 重新组装结果
            results = [''] * len(texts)
            result_idx = 0
            for orig_idx, _ in valid_texts:
                results[orig_idx] = all_results[result_idx] if result_idx < len(all_results) else texts[orig_idx]
                result_idx += 1

            return results

        except Exception as e:
            logger.error(f"腾讯批量翻译失败: {e}")
            raise

    def _create_smart_batches(self, texts: List[str]) -> List[List[str]]:
        """智能创建批次 - 最大化利用6000字符限制"""
        batches = []
        current_batch = []
        current_length = 0

        for text in texts:
            text_length = len(text)
            separator_length = len(self.batch_separator) if current_batch else 0

            # 检查是否超过单次请求限制
            if current_length + separator_length + text_length > self.max_chars_per_request:
                if current_batch:
                    batches.append(current_batch)
                    current_batch = [text]
                    current_length = text_length
                else:
                    # 单个文本就超过限制，需要分割
                    split_texts = self._split_long_text(text)
                    for split_text in split_texts:
                        batches.append([split_text])
            else:
                current_batch.append(text)
                current_length += separator_length + text_length

        if current_batch:
            batches.append(current_batch)

        logger.debug(f"创建了 {len(batches)} 个批次，平均每批 {len(texts)/len(batches):.1f} 个文本")
        return batches

    def _split_long_text(self, text: str) -> List[str]:
        """分割过长的文本"""
        if len(text) <= self.max_chars_per_request:
            return [text]

        # 按句号分割
        sentences = text.split('。')
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            if len(current_chunk) + len(sentence) + 1 <= self.max_chars_per_request:
                current_chunk += sentence + "。"
            else:
                if current_chunk:
                    chunks.append(current_chunk.rstrip('。'))
                current_chunk = sentence + "。"

        if current_chunk:
            chunks.append(current_chunk.rstrip('。'))

        return chunks

    def _translate_single_batch(self, texts: List[str]) -> List[str]:
        """翻译单个批次"""
        if not texts:
            return []

        # 频率控制
        self._rate_limit()

        try:
            from tencentcloud.tmt.v20180321 import models

            # 合并文本
            combined_text = self.batch_separator.join(texts)

            req = models.TextTranslateRequest()
            req.SourceText = combined_text
            req.Source = "zh"
            req.Target = "en"
            req.ProjectId = 0

            resp = self.client.TextTranslate(req)
            result = resp.TargetText

            # 分割结果
            translated_texts = result.split(self.batch_separator)

            # 确保结果数量匹配
            while len(translated_texts) < len(texts):
                translated_texts.append(texts[len(translated_texts)])

            logger.debug(f"腾讯批量翻译成功: {len(texts)} 个文本，消耗字符: {resp.UsedAmount}")
            return translated_texts[:len(texts)]

        except Exception as e:
            logger.error(f"腾讯单批次翻译失败: {e}")
            # 如果批量失败，尝试逐个翻译
            return self._fallback_individual_translate(texts)

    def _rate_limit(self):
        """频率限制控制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.request_interval:
            sleep_time = self.request_interval - time_since_last
            logger.debug(f"频率限制：等待 {sleep_time:.2f} 秒")
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def _fallback_individual_translate(self, texts: List[str]) -> List[str]:
        """回退到逐个翻译"""
        logger.warning("批量翻译失败，回退到逐个翻译")
        results = []

        for text in texts:
            try:
                self._rate_limit()

                from tencentcloud.tmt.v20180321 import models

                req = models.TextTranslateRequest()
                req.SourceText = text
                req.Source = "zh"
                req.Target = "en"
                req.ProjectId = 0

                resp = self.client.TextTranslate(req)
                results.append(resp.TargetText)

            except Exception as e:
                logger.error(f"逐个翻译失败: {text[:30]}... - {e}")
                results.append(text)  # 失败时保留原文

        return results

class HybridTranslator:
    """混合翻译器：Helsinki-NLP + 腾讯API备份"""

    def __init__(self, cloud_only: bool = False):
        # 初始化Helsinki本地翻译器
        self.local_translator_available = False
        self.helsinki_translator = None

        if not cloud_only:
            try:
                self.helsinki_translator = HelsinkiTranslator()
                self.local_translator_available = True
                logger.info("Helsinki-NLP本地翻译器初始化成功")
            except Exception as e:
                logger.warning(f"Helsinki-NLP翻译器初始化失败: {e}")
                logger.warning("将使用仅云端翻译模式")
        else:
            logger.info("使用仅云端翻译模式")

        # 初始化腾讯云翻译器
        self.tencent_translator = TencentTranslator()

        # 技术术语词典
        self.technical_terms = self._load_technical_terms()

        mode = "混合模式" if self.local_translator_available else "仅云端模式"
        logger.info(f"翻译器初始化完成，运行模式: {mode}")
    
    def _load_technical_terms(self) -> Dict[str, str]:
        """加载技术术语词典（保持兼容性）"""
        # 使用新的技术词典系统
        from .technical_dictionary import get_technical_dictionary
        tech_dict = get_technical_dictionary()
        return tech_dict.dictionary
    
    def translate(self, text: str) -> str:
        """智能翻译单个文本"""
        results = self.translate_batch([text])
        return results[0] if results else text

    def translate_batch(self, texts: List[str]) -> List[str]:
        """智能批量翻译 - 优化版本"""
        if not texts:
            return []

        # 导入技术词典
        from .technical_dictionary import get_technical_dictionary
        tech_dict = get_technical_dictionary()

        results = []
        cloud_needed_texts = []
        cloud_needed_indices = []

        # 第一阶段：技术词典 + 本地翻译
        for i, text in enumerate(texts):
            if not text or not text.strip():
                results.append(text)
                continue

            text_clean = text.strip()

            # 1. 优先使用技术词典（最准确）
            if tech_dict.is_technical_term(text_clean):
                result = tech_dict.translate_term(text_clean)
                logger.debug(f"技术词典: '{text}' -> '{result}'")
                results.append(result)
                continue

            # 2. 尝试本地翻译（仅对非技术术语）
            if self.local_translator_available and self.helsinki_translator:
                try:
                    result = self.helsinki_translator.translate(text)
                    if self._is_valid_translation(result, text):
                        logger.debug(f"本地翻译: '{text[:30]}...' -> '{result[:30]}...'")
                        results.append(result)
                        continue
                except Exception as e:
                    logger.debug(f"本地翻译失败: {text[:30]}... - {e}")

            # 3. 需要云端翻译的文本
            cloud_needed_texts.append(text)
            cloud_needed_indices.append(i)
            results.append("")  # 占位符

        # 第二阶段：批量云端翻译（仅翻译真正需要的内容）
        if cloud_needed_texts:
            logger.info(f"需要云端翻译 {len(cloud_needed_texts)} 个文本片段")
            try:
                cloud_results = self.tencent_translator.translate_batch(cloud_needed_texts)

                # 填充云端翻译结果
                for i, cloud_result in enumerate(cloud_results):
                    if i < len(cloud_needed_indices):
                        results[cloud_needed_indices[i]] = cloud_result
                        logger.debug(f"云端翻译: '{cloud_needed_texts[i][:30]}...' -> '{cloud_result[:30]}...'")

            except Exception as e:
                logger.warning(f"云端批量翻译失败: {e}")

                # 云端翻译失败，保留原文
                for i, original_text in enumerate(cloud_needed_texts):
                    if i < len(cloud_needed_indices):
                        results[cloud_needed_indices[i]] = original_text
                        logger.error(f"所有翻译方法失败，保留原文: '{original_text[:30]}...'")

        # 统计翻译覆盖率
        coverage = tech_dict.get_translation_coverage(texts)
        logger.info(f"技术词典覆盖率: {coverage['coverage_rate']:.1%} ({coverage['covered']}/{coverage['total']})")

        return results
    
    def _is_valid_translation(self, translated: str, original: str) -> bool:
        """验证翻译质量"""
        if not translated or not translated.strip():
            return False

        # 如果翻译结果和原文相同，认为翻译失败
        if translated.strip() == original.strip():
            return False

        # 检查是否还包含大量中文字符
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', translated))
        if chinese_chars > len(original) * 0.3:  # 如果中文字符超过30%，认为翻译不完整
            return False

        # 检查翻译质量问题
        if self._has_translation_issues(translated, original):
            return False

        return True

    def _has_translation_issues(self, translated: str, original: str) -> bool:
        """检查翻译是否有质量问题"""
        # 检查数值格式是否被破坏
        import re

        # 检查是否有明显的翻译错误模式
        error_patterns = [
            r'\d+\d+(?:mPa|g/cm|℃)',  # 数字连在一起，如 "400200mPa"
            r'\d+\s+\d+\s+\d+',  # 数字被异常分割，如 "1.10 0.000.05"
            r'[A-Za-z]{25,}',  # 过长的英文单词（可能是错误翻译）
            r'\d+times',  # 错误的指数翻译，如 "1011times"
        ]

        for pattern in error_patterns:
            if re.search(pattern, translated):
                logger.debug(f"检测到翻译质量问题: {pattern} in '{translated}'")
                return True

        return False
