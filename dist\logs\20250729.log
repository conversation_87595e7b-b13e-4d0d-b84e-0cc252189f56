2025-07-29 16:31:30,655 - src.utils.config_manager - INFO - logger.py:76 - 成功加载配置文件: config.json
2025-07-29 16:31:30,656 - src.utils.config_manager - DEBUG - logger.py:72 - 学习映射已保存: learned_mappings.json
2025-07-29 16:31:30,896 - src.matching.fuzzy_matcher - DEBUG - logger.py:72 - 重建关键词索引完成，关键词数量: 58
2025-07-29 16:31:30,896 - src.matching.fuzzy_matcher - DEBUG - logger.py:72 - 模糊匹配器初始化完成，阈值: 85.0，关键词数量: 58
2025-07-29 16:31:30,896 - __main__ - INFO - logger.py:76 - 智能汇总表生成器 v2.0 初始化完成
2025-07-29 16:31:30,896 - __main__ - INFO - logger.py:76 - 开始处理文件夹: PDF
2025-07-29 16:31:30,896 - __main__ - INFO - logger.py:76 - 输出文件: 智能汇总表.xlsx
2025-07-29 16:31:30,899 - __main__ - ERROR - logger.py:84 - 程序执行出错: 'NoneType' object has no attribute 'write'
2025-07-29 16:31:30,900 - __main__ - ERROR - logger.py:84 - Traceback (most recent call last):
  File "main.py", line 215, in main
  File "main.py", line 56, in process_folder
  File "src\utils\progress.py", line 152, in create_progress_tracker
    return ProgressTracker(total_files, description)
  File "src\utils\progress.py", line 27, in __init__
    self.pbar = tqdm(
                ~~~~^
        total=total_files,
        ^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]"
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "tqdm\std.py", line 1098, in __init__
  File "tqdm\std.py", line 1347, in refresh
  File "tqdm\std.py", line 1495, in display
  File "tqdm\std.py", line 459, in print_status
  File "tqdm\std.py", line 452, in fp_write
  File "tqdm\utils.py", line 140, in __getattr__
AttributeError: 'NoneType' object has no attribute 'write'

