#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试性能特点提取功能
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils import get_logger
from src.summary_table import ConfigBasedExtractor, SummaryTableConfig
from src.extraction import DocxExtractor, PDFExtractor

logger = get_logger(__name__)


def debug_specific_file(file_path: str):
    """调试特定文件的性能特点提取"""
    print(f"🔍 调试文件: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        # 初始化提取器
        config = SummaryTableConfig()
        extractor = ConfigBasedExtractor(config)
        
        # 获取原始内容
        if file_path.endswith('.pdf'):
            content_extractor = PDFExtractor()
        else:
            content_extractor = DocxExtractor()
        
        content = content_extractor.extract_content(file_path)
        
        if not content:
            print("❌ 无法提取文档内容")
            return False
        
        print(f"📄 文档信息:")
        print(f"   标题: {content.title}")
        print(f"   表格数: {len(content.tables) if content.tables else 0}")
        print(f"   段落数: {len(content.paragraphs) if content.paragraphs else 0}")
        
        # 显示段落内容
        if content.paragraphs:
            print(f"\n📝 段落内容:")
            for i, paragraph in enumerate(content.paragraphs):
                if paragraph.strip():
                    print(f"   段落{i+1}: {paragraph}")
        else:
            print(f"\n📝 没有段落内容")
        
        # 显示表格内容
        if content.tables:
            print(f"\n📊 表格内容:")
            for table_idx, table in enumerate(content.tables):
                print(f"   表格{table_idx+1}:")
                for row_idx, row in enumerate(table.data[:10]):  # 只显示前10行
                    print(f"     行{row_idx+1}: {row}")
                if len(table.data) > 10:
                    print(f"     ... (还有 {len(table.data) - 10} 行)")
        
        # 测试性能特点提取
        print(f"\n🔍 测试性能特点提取:")
        performance_features = extractor._extract_performance_features(content)
        
        if performance_features:
            print(f"✅ 性能特点提取成功:")
            print(f"📝 内容: {performance_features}")
        else:
            print(f"❌ 未提取到性能特点")
            
            # 手动检查可能的性能特点
            print(f"\n🔍 手动检查可能的性能特点:")
            feature_keywords = ["产品陈述", "性能特点", "产品描述", "产品概述"]
            
            if content.tables:
                for table_idx, table in enumerate(content.tables):
                    print(f"   检查表格{table_idx+1}:")
                    for row_idx, row in enumerate(table.data):
                        if len(row) >= 2:
                            param_name = str(row[0]).strip()
                            param_value = str(row[1]).strip()
                            
                            # 检查是否匹配关键词
                            if param_name in feature_keywords:
                                print(f"     ✅ 找到匹配: {param_name} = {param_value}")
                            elif any(keyword in param_name for keyword in feature_keywords):
                                print(f"     🔍 部分匹配: {param_name} = {param_value}")
                            
                            # 检查是否看起来像产品描述
                            if len(param_value) > 20:
                                looks_like_desc = extractor._looks_like_product_description(param_value)
                                if looks_like_desc:
                                    print(f"     💡 可能的描述: {param_name} = {param_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 性能特点提取调试工具")
    print("=" * 60)
    
    # 扫描文件
    test_folder = "PDF"
    if not os.path.exists(test_folder):
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return
    
    files = []
    for ext in ["*.pdf", "*.docx"]:
        files.extend(list(Path(test_folder).glob(ext)))
    
    if not files:
        print("❌ 没有找到测试文件")
        return
    
    print(f"📁 找到 {len(files)} 个文件:")
    for i, file_path in enumerate(files, 1):
        print(f"  {i}. {file_path.name}")
    
    try:
        choice = input(f"\n请选择要调试的文件 (1-{len(files)}): ")
        file_index = int(choice) - 1
        
        if file_index < 0 or file_index >= len(files):
            print("❌ 无效的文件编号")
            return
        
        selected_file = str(files[file_index])
        print(f"\n✅ 选择文件: {selected_file}")
        print()
        
        # 执行调试
        debug_specific_file(selected_file)
        
    except KeyboardInterrupt:
        print("\n👋 调试已取消")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 调试出错: {e}")


if __name__ == "__main__":
    main()
