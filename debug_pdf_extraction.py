#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PDF提取问题
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.extraction import PDFExtractor
from src.summary_table import ConfigBasedExtractor, SummaryTableConfig
from src.utils import get_logger

logger = get_logger(__name__)


def debug_pdf_raw_extraction(file_path: str):
    """调试PDF原始提取"""
    print("=" * 80)
    print(f"🔍 调试PDF原始提取: {os.path.basename(file_path)}")
    print("=" * 80)
    
    extractor = PDFExtractor()
    content = extractor.extract_content(file_path)
    
    if not content:
        print("❌ 内容提取失败")
        return
    
    print(f"📄 基本信息:")
    print(f"   标题: {content.title}")
    print(f"   表格数量: {len(content.tables)}")
    print(f"   段落数量: {len(content.paragraphs)}")
    
    # 详细显示每个表格的原始数据
    for i, table in enumerate(content.tables):
        print(f"\n📊 表格 {i+1} (页面 {table.page}):")
        print(f"   表头: {table.headers}")
        print(f"   数据行数: {len(table.data)}")
        
        # 显示前几行数据
        for j, row in enumerate(table.data[:10]):  # 只显示前10行
            print(f"     行{j+1}: {row}")
        
        if len(table.data) > 10:
            print(f"     ... (还有 {len(table.data) - 10} 行)")


def debug_config_based_extraction(file_path: str):
    """调试配置驱动提取"""
    print("\n" + "=" * 80)
    print(f"🔍 调试配置驱动提取: {os.path.basename(file_path)}")
    print("=" * 80)
    
    config = SummaryTableConfig()
    extractor = ConfigBasedExtractor(config)
    
    # 获取原始内容
    pdf_extractor = PDFExtractor()
    content = pdf_extractor.extract_content(file_path)
    
    if not content:
        print("❌ 内容提取失败")
        return
    
    # 调试表格解析
    for i, table in enumerate(content.tables):
        print(f"\n📊 调试表格 {i+1}:")
        
        # 检查是否为AB表格
        is_ab = extractor._is_ab_table(table.data)
        print(f"   是否为AB表格: {is_ab}")
        
        # 查找列结构
        param_col, value_cols = extractor._find_table_columns(table.data)
        print(f"   参数列: {param_col}")
        print(f"   值列: {value_cols}")
        
        # 显示列标题分析
        if table.data:
            print(f"   表头分析:")
            for col_idx, header in enumerate(table.data[0] if table.data else []):
                print(f"     列{col_idx}: '{header}'")
                if is_ab:
                    ab_key = extractor._generate_ab_key("测试参数", col_idx, table.data)
                    print(f"       -> AB键: {ab_key}")
        
        # 提取参数
        table_params = extractor._extract_from_table(table)
        print(f"   提取的参数 ({len(table_params)} 个):")
        for key, value in table_params.items():
            print(f"     {key}: {value}")


def main():
    """主函数"""
    # 测试文件
    test_files = [
        "PDF/FM-109-17技术参数表.pdf",
        "PDF/FM-109-17技术参数表20250101.pdf",
        "PDF/FM-3602-1技术参数表.pdf"
    ]
    
    print("🔧 PDF提取调试工具")
    print("=" * 80)
    
    for i, file_path in enumerate(test_files, 1):
        if os.path.exists(file_path):
            print(f"{i}. {os.path.basename(file_path)}")
    
    try:
        choice = input(f"\n请选择要调试的文件 (1-{len(test_files)}): ")
        file_index = int(choice) - 1
        
        if file_index < 0 or file_index >= len(test_files):
            print("❌ 无效的文件编号")
            return
        
        selected_file = test_files[file_index]
        
        if not os.path.exists(selected_file):
            print(f"❌ 文件不存在: {selected_file}")
            return
        
        # 执行调试
        debug_pdf_raw_extraction(selected_file)
        debug_config_based_extraction(selected_file)
        
    except KeyboardInterrupt:
        print("\n👋 调试已取消")
    except ValueError:
        print("❌ 请输入有效的数字")
    except Exception as e:
        print(f"❌ 调试出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
